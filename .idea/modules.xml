<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/packages/utils/analysis/melos_analysis.iml" filepath="$PROJECT_DIR$/packages/utils/analysis/melos_analysis.iml" />
      <module fileurl="file://$PROJECT_DIR$/packages/core/app_di/melos_app_di.iml" filepath="$PROJECT_DIR$/packages/core/app_di/melos_app_di.iml" />
      <module fileurl="file://$PROJECT_DIR$/packages/features/auth/melos_auth.iml" filepath="$PROJECT_DIR$/packages/features/auth/melos_auth.iml" />
      <module fileurl="file://$PROJECT_DIR$/packages/features/children/melos_children.iml" filepath="$PROJECT_DIR$/packages/features/children/melos_children.iml" />
      <module fileurl="file://$PROJECT_DIR$/packages/core/design_system/melos_design_system.iml" filepath="$PROJECT_DIR$/packages/core/design_system/melos_design_system.iml" />
      <module fileurl="file://$PROJECT_DIR$/packages/core/flavors/melos_flavors.iml" filepath="$PROJECT_DIR$/packages/core/flavors/melos_flavors.iml" />
      <module fileurl="file://$PROJECT_DIR$/packages/utils/prelude/melos_prelude.iml" filepath="$PROJECT_DIR$/packages/utils/prelude/melos_prelude.iml" />
      <module fileurl="file://$PROJECT_DIR$/apps/uniclient/melos_uniclient.iml" filepath="$PROJECT_DIR$/apps/uniclient/melos_uniclient.iml" />
      <module fileurl="file://$PROJECT_DIR$/packages/core/uniclient_navigation/melos_uniclient_navigation.iml" filepath="$PROJECT_DIR$/packages/core/uniclient_navigation/melos_uniclient_navigation.iml" />
      <module fileurl="file://$PROJECT_DIR$/melos_lunch_manager.iml" filepath="$PROJECT_DIR$/melos_lunch_manager.iml" />
    </modules>
  </component>
</project>