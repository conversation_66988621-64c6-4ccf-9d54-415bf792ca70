name: lunch_manager

packages:
  - apps/**
  - packages/**

ignore:
  - "apps/**/build"
  - "packages/**/example/"

scripts:
  analyze-ci:
    run: |
      melos exec -c 1 --fail-fast -- \
      dart analyze . --fatal-infos
    description: |
      Run `dart analyze` in all packages.
  
  dependency-cycles:
    run: | 
      melos exec -- "lakos --metrics . > /dev/null"
    description: 
      Validates if the dependency graph contanins dependency cycles

  pub-get-p:
    run: |
      melos exec -- \
      "echo \"Running pub get\"; dart pub get --suppress-analytics &>/dev/null"
    description: | 
      Run `dart pub get` in parallel all packages.

  pub-get-o:
    run: |
      melos exec -- \
      echo \"Running pub get\"; dart pub get --suppress-analytics --offline &>/dev/null
    description: | 
      Run `dart pub get --offline` in all packages.

  pub-get-changes:
    run: |
      melos exec -c 1 --fail-fast --diff=origin/main...HEAD --include-dependents -- \
      dart pub get --suppress-analytics
    description: | 
      Run `dart pub get` in all changed packages.

  test:all:
    run: |
      melos run test --no-select && \
      melos run test:web --no-select
    description: |
      Run all tests available.

  test:
    run: |
      melos exec -c 6 --fail-fast -- \
      "flutter test --no-pub --coverage"
    description: Run `flutter test` for a specific package.
    packageFilters:
      dirExists:
        - test
      ignore:
        - "*web*"
        - "*example*"

  test:web:
    run: |
      melos exec -c 1 --fail-fast -- \
      "flutter test --platform=chrome --no-pub --coverage"
    description: Run `flutter test --platform=chrome` for a specific '*web' package.
    packageFilters:
      dirExists:
        - test
      scope: "*web*"

  run-builder:
    run: |
      melos exec -c 1 -- dart run build_runner build --delete-conflicting-outputs
    description: Run `build_runner` in all packages that depend on it
    packageFilters:
      depends_on:
        - build_runner

  fix-and-format:
    run: |
      melos run fix && melos run format
    description: Run `dart fix` and `dart format` in all packages
  
  fix:
    run: |
      melos exec -c 1 --fail-fast -- \
      "dart fix --apply"
    description: Run `dart fix` in all packages
  
  format:
    run: |
      melos exec -c 1 --fail-fast -- \
      "dart format ."
    description: Run `dart format` in all packages

  postclean: >
    melos exec -c 6 -- \
    rm -rf ./build ./android/.gradle ./ios./symlinks ./ios/Pods ./android/.idea ./.dart-tool/build
