import 'package:app_di/app_di.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_web_frame/flutter_web_frame.dart';
import 'package:uniclient_navigation/uniclient_navigation.dart';

class ClientApp extends StatelessWidget {
  const ClientApp({
    super.key,
    this.builder,
  });

  final TransitionBuilder? builder;

  @override
  Widget build(BuildContext context) {
    final router = diContainer<ClientRouter>();
    return FlutterWebFrame(
      maximumSize: const Size(600.0, 812.0),
      backgroundColor: Colors.grey.shade100,
      enabled: kIsWeb,
      builder: (_) => MaterialApp.router(
        theme: AppTheme.light,
        darkTheme: AppTheme.dark,
        themeMode: ThemeMode.light,
        routerConfig: router.config(),
        debugShowCheckedModeBanner: false,
        locale: const Locale('es_ES'),
        supportedLocales: const [
          Locale('es', 'ES'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        builder: (context, navigator) =>
            builder != null ? builder!(context, navigator) : navigator!,
      ),
    );
  }
}
