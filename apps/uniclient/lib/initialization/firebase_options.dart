// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBaT1km7aL5aySieB_fniVMGS0PW1OX5T4',
    appId: '1:713779617619:web:084afd6ab10cc3e1934ba6',
    messagingSenderId: '713779617619',
    projectId: 'lunch-manager-wip',
    authDomain: 'lunch-manager-wip.firebaseapp.com',
    storageBucket: 'lunch-manager-wip.appspot.com',
    measurementId: 'G-PT9VWFQZ69',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDPjG5cxYDZ2xAPeHzSw3LvjxdaNtdWVhI',
    appId: '1:713779617619:android:1548cdb457066502934ba6',
    messagingSenderId: '713779617619',
    projectId: 'lunch-manager-wip',
    storageBucket: 'lunch-manager-wip.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDFQcqIthNlDTJ-v2_t_6C_QwhYarV55I4',
    appId: '1:713779617619:ios:8b8db313c560de28934ba6',
    messagingSenderId: '713779617619',
    projectId: 'lunch-manager-wip',
    storageBucket: 'lunch-manager-wip.appspot.com',
    iosBundleId: 'com.example.uniclient',
  );
}
