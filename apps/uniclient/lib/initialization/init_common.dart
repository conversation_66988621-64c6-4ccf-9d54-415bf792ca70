import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:flavors/flavors.dart';
import 'package:flutter/material.dart';
import 'package:uniclient/di/di_initializers.dart';
import 'package:uniclient/initialization/firebase_options.dart';

Future<bool> initCommon({
  required FlavorConfig flavorConfig,
  FutureOr<void> Function()? flavorInit,
  void Function(String)? onInitializationStarted,
}) async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await initializeDI(flavorConfig, onInitializationStarted);
  return true;
}
