import 'dart:async';

import 'package:flavors/flavors.dart';
import 'package:flutter/material.dart';
import 'package:uniclient/client_app.dart';
import 'package:uniclient/utility/client_di_initializer.dart';

class InitSplash extends StatefulWidget {
  const InitSplash({
    required this.flavorConfig,
    super.key,
    this.flavorInit,
    this.builder,
  });

  final FlavorConfig flavorConfig;
  final FutureOr<void> Function()? flavorInit;
  final TransitionBuilder? builder;

  @override
  State<InitSplash> createState() => _InitSplashState();
}

class _InitSplashState extends State<InitSplash> {
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Stack(
        fit: StackFit.expand,
        children: [
          UniClientDIInitializer(
            flavorConfig: widget.flavorConfig,
            builder: (_) => ClientApp(builder: widget.builder!),
          ),
        ],
      ),
    );
  }
}
