import 'dart:async';

import 'package:design_system/design_system.dart';
import 'package:flavors/flavors.dart';
import 'package:flutter/material.dart';
import 'package:uniclient/initialization/init_common.dart';
import 'package:uniclient/utility/failed_to_initialize.dart';

class UniClientDIInitializer extends StatefulWidget {
  const UniClientDIInitializer({
    required this.builder,
    required this.flavorConfig,
    super.key,
    this.flavorInit,
  });

  final WidgetBuilder builder;
  final FlavorConfig flavorConfig;
  final FutureOr<void> Function()? flavorInit;

  @override
  State<UniClientDIInitializer> createState() => _UniClientDIInitializerState();
}

class _UniClientDIInitializerState extends State<UniClientDIInitializer> {
  late final Future<bool> _initializationFuture = initCommon(
    flavorConfig: widget.flavorConfig,
    onInitializationStarted: (i) => currentInitializer = i,
  );

  String? currentInitializer;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _initializationFuture.timeout(
        const Duration(seconds: 60),
        onTimeout: () => false,
      ),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return FailToInitialize(
            lastInitializer: currentInitializer,
            error: snapshot.error,
          );
        } else if (snapshot.hasData) {
          return widget.builder(context);
        } else {
          return ColoredBox(
            color: context.appTheme.scaffoldBackgroundColor,
          );
        }
      },
    );
  }
}
