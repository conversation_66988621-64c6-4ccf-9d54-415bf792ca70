import 'package:app_di/app_di.dart';
import 'package:flavors/flavors.dart';
import 'package:flutter/material.dart';

class VersionBanner extends StatelessWidget {
  const VersionBanner({
    required this.flavorConfig,
    super.key,
    this.child,
  });

  final Widget? child;
  final FlavorConfig flavorConfig;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Banner(
          message: diContainer<String>(
            name: CoreDiParameterName.environmentName,
          ).toUpperCase(),
          location: BannerLocation.topEnd,
          child: child,
        ),
      ],
    );
  }
}
