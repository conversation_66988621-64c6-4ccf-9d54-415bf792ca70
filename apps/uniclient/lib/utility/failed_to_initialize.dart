import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';

// TODO: Improve to handle DI initialization issues
class FailToInitialize extends StatelessWidget {
  const FailToInitialize({
    super.key,
    this.lastInitializer,
    this.error,
  });

  final String? lastInitializer;
  final Object? error;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: dimen.all.md,
        child: const Column(
          children: [
            Text('An error has occured'),
          ],
        ),
      ),
    );
  }
}
