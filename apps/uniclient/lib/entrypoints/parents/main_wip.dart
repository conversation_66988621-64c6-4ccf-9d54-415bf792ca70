import 'package:flavors/flavors.dart';
import 'package:flutter/material.dart';
import 'package:uniclient/initialization/init_splash.dart';
import 'package:uniclient/utility/version_banner.dart';

Future<void> main() async {
  runApp(
    InitSplash(
      flavorConfig: parentsWipFlavorConfig,
      builder: (context, child) => VersionBanner(
        flavorConfig: parentsWipFlavorConfig,
        child: child,
      ),
    ),
  );
}
