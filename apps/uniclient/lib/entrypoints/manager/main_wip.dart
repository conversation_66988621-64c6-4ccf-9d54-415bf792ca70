import 'package:flavors/flavors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uniclient/initialization/init_splash.dart';
import 'package:uniclient/utility/version_banner.dart';

Future<void> main() async {
  Bloc.observer = SimpleBlocObserver();
  runApp(
    InitSplash(
      flavorConfig: managerWipFlavorConfig,
      builder: (context, child) => VersionBanner(
        flavorConfig: managerWipFlavorConfig,
        child: child,
      ),
    ),
  );
}

class SimpleBlocObserver extends BlocObserver {
  @override
  void onEvent(Bloc<dynamic, dynamic> bloc, Object? event) {
    super.onEvent(bloc, event);
    debugPrint('Bloc Event: $event');
  }

  @override
  void onChange(BlocBase<dynamic> bloc, Change<dynamic> change) {
    super.onChange(bloc, change);
    debugPrint('Bloc Change: $change');
  }

  @override
  void onTransition(
    Bloc<dynamic, dynamic> bloc,
    Transition<dynamic, dynamic> transition,
  ) {
    super.onTransition(bloc, transition);
    debugPrint('Bloc Transition: $transition');
  }

  @override
  void onError(BlocBase<dynamic> bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    debugPrint('Bloc Error: $error');
  }
}
