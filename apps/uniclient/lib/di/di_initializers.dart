import 'package:app_di/app_di.dart';
import 'package:auth/auth.dart';
import 'package:children/children.dart';
import 'package:flavors/flavors.dart';
import 'package:uniclient/di/client/di_initializer.dart';
import 'package:uniclient_navigation/uniclient_navigation.dart';

Future<DIContainer> initializeDI(
  FlavorConfig config, [
  void Function(String)? onInitializationStarted,
]) => initializeDIContainer(
  [
    ClientFlavorsDIInitializer(config),
    const ClientAppDiInitializer(),
    const ClientNavigationDIInitializer(),
    const AuthDIInitializer(),
    const ChildrenDIInitializer(),
  ],
  onInitializationStarted: onInitializationStarted,
);
