flavors:
  parentsWip:
    app:
      name: "LM - Parents WIP"
  
    android:
      applicationId: "es.lunchmanager.parentsWip"
      firebase:
        config: "../../.firebase/parentsWip/google-services.json"
  
    ios:
      bundleId: "es.lunchmanager.parentsWip"
      firebase:
        config: "../../.firebase/parentsWip/GoogleService-Info.plist"
  
  parentsProd:
    app:
      name: "LM - Parents"
      
    android:
      applicationId: "es.lunchmanager.parentsWip"
      firebase:
        config: "../../.firebase/parentsProd/google-services.json"
    ios:
      bundleId: "es.lunchmanager.parentsWip"
      firebase:
        config: "../../.firebase/parentsProd/GoogleService-Info.plist"

  managerWip:
    app:
      name: "LM - Manager WIP"
  
    android:
      applicationId: "es.lunchmanager.managerWip"
      firebase:
        config: "../../.firebase/managerWip/google-services.json"
  
    ios:
      bundleId: "es.lunchmanager.managerWip"
      firebase:
        config: "../../.firebase/managerWip/GoogleService-Info.plist"
  
  managerProd:
    app:
      name: "LM - Manager"
      
    android:
      applicationId: "es.lunchmanager.managerWip"
      firebase:
        config: "../../.firebase/managerProd/google-services.json"
    ios:
      bundleId: "es.lunchmanager.managerWip"
      firebase:
        config: "../../.firebase/managerProd/GoogleService-Info.plist"