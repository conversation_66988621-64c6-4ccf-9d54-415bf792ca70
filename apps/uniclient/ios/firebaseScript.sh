if [ "$CONFIGURATION" == "Debug-parentsWip" ] || [ "$CONFIGURATION" == "Release-parentsWip" ]; then
  cp Runner/parentsWip/GoogleService-Info.plist Runner/GoogleService-Info.plist
elif [ "$CONFIGURATION" == "Debug-parentsProd" ] || [ "$CONFIGURATION" == "Release-parentsProd" ]; then
  cp Runner/parentsProd/GoogleService-Info.plist Runner/GoogleService-Info.plist
elif [ "$CONFIGURATION" == "Debug-managerWip" ] || [ "$CONFIGURATION" == "Release-managerWip" ]; then
  cp Runner/managerWip/GoogleService-Info.plist Runner/GoogleService-Info.plist
elif [ "$CONFIGURATION" == "Debug-managerProd" ] || [ "$CONFIGURATION" == "Release-managerProd" ]; then
  cp Runner/managerProd/GoogleService-Info.plist Runner/GoogleService-Info.plist
fi

