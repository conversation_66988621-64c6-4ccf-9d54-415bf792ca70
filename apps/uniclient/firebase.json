{"flutter": {"platforms": {"android": {"default": {"projectId": "lunch-manager-wip", "appId": "1:713779617619:android:1548cdb457066502934ba6", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "lunch-manager-wip", "appId": "1:713779617619:ios:8b8db313c560de28934ba6", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "lunch-manager-wip", "configurations": {"android": "1:713779617619:android:1548cdb457066502934ba6", "ios": "1:713779617619:ios:8b8db313c560de28934ba6", "web": "1:713779617619:web:084afd6ab10cc3e1934ba6"}}}}}, "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "frameworksBackend": {"region": "europe-west1"}}}