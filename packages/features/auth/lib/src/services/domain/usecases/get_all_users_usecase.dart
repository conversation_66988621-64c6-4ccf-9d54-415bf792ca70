import 'package:auth/src/domain/models/user_details.dart';
import 'package:auth/src/domain/repository/profile_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class GetAllUsersUsecase {
  GetAllUsersUsecase(this._profileRepository);

  final ProfileRepository _profileRepository;

  Future<Result<Iterable<UserDetails>, Exception>> call() =>
      _profileRepository.getAllUsers();
}
