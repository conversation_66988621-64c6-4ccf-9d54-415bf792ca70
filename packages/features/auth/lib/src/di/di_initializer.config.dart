// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auth/src/di/di_module.dart' as _i669;
import 'package:auth/src/domain/provider/auth_firebase_provider.dart' as _i676;
import 'package:auth/src/domain/provider/profile_firebase_storage_provider.dart'
    as _i432;
import 'package:auth/src/domain/repository/auth_repository.dart' as _i973;
import 'package:auth/src/domain/repository/profile_repository.dart' as _i547;
import 'package:auth/src/domain/usecases/get_user_details_usecase.dart'
    as _i598;
import 'package:auth/src/domain/usecases/save_user_details_usecase.dart'
    as _i370;
import 'package:auth/src/domain/usecases/sign_in_usecase.dart' as _i414;
import 'package:auth/src/domain/usecases/sign_up_usecase.dart' as _i125;
import 'package:auth/src/navigator/auth_navigator.dart' as _i97;
import 'package:auth/src/presentation/authorization/bloc/auth_bloc.dart' as _i7;
import 'package:auth/src/presentation/email_verification/bloc/email_verification_bloc.dart'
    as _i774;
import 'package:auth/src/presentation/initial_verification/bloc/initial_verification_bloc.dart'
    as _i555;
import 'package:auth/src/presentation/register_user_profile/bloc/register_user_profile_bloc.dart'
    as _i245;
import 'package:auth/src/services/domain/usecases/get_all_users_usecase.dart'
    as _i37;
import 'package:auth/src/services/domain/usecases/get_current_user_usecase.dart'
    as _i835;
import 'package:auth/src/services/domain/usecases/sign_out_usecase.dart'
    as _i432;
import 'package:cloud_firestore/cloud_firestore.dart' as _i974;
import 'package:firebase_auth/firebase_auth.dart' as _i59;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    final firebaseInstanceProvider = _$FirebaseInstanceProvider();
    gh.singleton<_i59.FirebaseAuth>(
      () => firebaseInstanceProvider.authInstance(),
    );
    gh.singleton<_i974.FirebaseFirestore>(
      () => firebaseInstanceProvider.storeInstance(),
    );
    gh.factory<_i432.ProfileFirebaseStorageProvider>(
      () => _i432.ProfileFirebaseStorageProvider(gh<_i974.FirebaseFirestore>()),
    );
    gh.factory<_i547.ProfileRepository>(
      () => _i547.ProfileRepository(gh<_i432.ProfileFirebaseStorageProvider>()),
    );
    gh.factory<_i37.GetAllUsersUsecase>(
      () => _i37.GetAllUsersUsecase(gh<_i547.ProfileRepository>()),
    );
    gh.factory<_i676.AuthFirebaseProvider>(
      () => _i676.AuthFirebaseProvider(gh<_i59.FirebaseAuth>()),
    );
    gh.factory<_i973.AuthRepository>(
      () => _i973.AuthRepository(gh<_i676.AuthFirebaseProvider>()),
    );
    gh.factory<_i598.GetUserDetailsUsecase>(
      () => _i598.GetUserDetailsUsecase(gh<_i547.ProfileRepository>()),
    );
    gh.factory<_i370.SaveUserDetailsUsecase>(
      () => _i370.SaveUserDetailsUsecase(gh<_i547.ProfileRepository>()),
    );
    gh.factory<_i414.SignInUsecase>(
      () => _i414.SignInUsecase(gh<_i973.AuthRepository>()),
    );
    gh.factory<_i125.SignUpUsecase>(
      () => _i125.SignUpUsecase(gh<_i973.AuthRepository>()),
    );
    gh.factory<_i835.GetCurrentUserUsecase>(
      () => _i835.GetCurrentUserUsecase(gh<_i973.AuthRepository>()),
    );
    gh.factory<_i432.SignOutUsecase>(
      () => _i432.SignOutUsecase(gh<_i973.AuthRepository>()),
    );
    gh.factory<_i245.RegisterUserProfileBloc>(
      () => _i245.RegisterUserProfileBloc(
        gh<_i835.GetCurrentUserUsecase>(),
        gh<_i370.SaveUserDetailsUsecase>(),
        gh<_i97.AuthNavigator>(),
      ),
    );
    gh.factory<_i555.InitialVerificationBloc>(
      () => _i555.InitialVerificationBloc(
        gh<_i97.AuthNavigator>(),
        gh<_i835.GetCurrentUserUsecase>(),
        gh<_i598.GetUserDetailsUsecase>(),
      ),
    );
    gh.factory<_i774.EmailVerificationBloc>(
      () => _i774.EmailVerificationBloc(
        gh<_i432.SignOutUsecase>(),
        gh<_i97.AuthNavigator>(),
      ),
    );
    gh.factoryParam<_i7.AuthBloc, _i7.AuthType, dynamic>(
      (authType, _) => _i7.AuthBloc(
        gh<_i414.SignInUsecase>(),
        gh<_i125.SignUpUsecase>(),
        gh<_i97.AuthNavigator>(),
        gh<_i835.GetCurrentUserUsecase>(),
        authType,
        gh<_i598.GetUserDetailsUsecase>(),
      ),
    );
    return this;
  }
}

class _$FirebaseInstanceProvider extends _i669.FirebaseInstanceProvider {}
