// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AuthEvent implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent()';
}


}

/// @nodoc
class $AuthEventCopyWith<$Res>  {
$AuthEventCopyWith(AuthEvent _, $Res Function(AuthEvent) __);
}


/// @nodoc


class _LoadAuthEvent with DiagnosticableTreeMixin implements AuthEvent {
  const _LoadAuthEvent(this.authType);
  

 final  AuthType authType;

/// Create a copy of AuthEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoadAuthEventCopyWith<_LoadAuthEvent> get copyWith => __$LoadAuthEventCopyWithImpl<_LoadAuthEvent>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.load'))
    ..add(DiagnosticsProperty('authType', authType));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadAuthEvent&&(identical(other.authType, authType) || other.authType == authType));
}


@override
int get hashCode => Object.hash(runtimeType,authType);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.load(authType: $authType)';
}


}

/// @nodoc
abstract mixin class _$LoadAuthEventCopyWith<$Res> implements $AuthEventCopyWith<$Res> {
  factory _$LoadAuthEventCopyWith(_LoadAuthEvent value, $Res Function(_LoadAuthEvent) _then) = __$LoadAuthEventCopyWithImpl;
@useResult
$Res call({
 AuthType authType
});




}
/// @nodoc
class __$LoadAuthEventCopyWithImpl<$Res>
    implements _$LoadAuthEventCopyWith<$Res> {
  __$LoadAuthEventCopyWithImpl(this._self, this._then);

  final _LoadAuthEvent _self;
  final $Res Function(_LoadAuthEvent) _then;

/// Create a copy of AuthEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? authType = null,}) {
  return _then(_LoadAuthEvent(
null == authType ? _self.authType : authType // ignore: cast_nullable_to_non_nullable
as AuthType,
  ));
}


}

/// @nodoc


class _UpdateEmailAuthEvent with DiagnosticableTreeMixin implements AuthEvent {
  const _UpdateEmailAuthEvent(this.email);
  

 final  String email;

/// Create a copy of AuthEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateEmailAuthEventCopyWith<_UpdateEmailAuthEvent> get copyWith => __$UpdateEmailAuthEventCopyWithImpl<_UpdateEmailAuthEvent>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.updateEmail'))
    ..add(DiagnosticsProperty('email', email));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateEmailAuthEvent&&(identical(other.email, email) || other.email == email));
}


@override
int get hashCode => Object.hash(runtimeType,email);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.updateEmail(email: $email)';
}


}

/// @nodoc
abstract mixin class _$UpdateEmailAuthEventCopyWith<$Res> implements $AuthEventCopyWith<$Res> {
  factory _$UpdateEmailAuthEventCopyWith(_UpdateEmailAuthEvent value, $Res Function(_UpdateEmailAuthEvent) _then) = __$UpdateEmailAuthEventCopyWithImpl;
@useResult
$Res call({
 String email
});




}
/// @nodoc
class __$UpdateEmailAuthEventCopyWithImpl<$Res>
    implements _$UpdateEmailAuthEventCopyWith<$Res> {
  __$UpdateEmailAuthEventCopyWithImpl(this._self, this._then);

  final _UpdateEmailAuthEvent _self;
  final $Res Function(_UpdateEmailAuthEvent) _then;

/// Create a copy of AuthEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? email = null,}) {
  return _then(_UpdateEmailAuthEvent(
null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _UpdatePasswordAuthEvent with DiagnosticableTreeMixin implements AuthEvent {
  const _UpdatePasswordAuthEvent(this.password);
  

 final  String password;

/// Create a copy of AuthEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdatePasswordAuthEventCopyWith<_UpdatePasswordAuthEvent> get copyWith => __$UpdatePasswordAuthEventCopyWithImpl<_UpdatePasswordAuthEvent>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.updatePassword'))
    ..add(DiagnosticsProperty('password', password));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdatePasswordAuthEvent&&(identical(other.password, password) || other.password == password));
}


@override
int get hashCode => Object.hash(runtimeType,password);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.updatePassword(password: $password)';
}


}

/// @nodoc
abstract mixin class _$UpdatePasswordAuthEventCopyWith<$Res> implements $AuthEventCopyWith<$Res> {
  factory _$UpdatePasswordAuthEventCopyWith(_UpdatePasswordAuthEvent value, $Res Function(_UpdatePasswordAuthEvent) _then) = __$UpdatePasswordAuthEventCopyWithImpl;
@useResult
$Res call({
 String password
});




}
/// @nodoc
class __$UpdatePasswordAuthEventCopyWithImpl<$Res>
    implements _$UpdatePasswordAuthEventCopyWith<$Res> {
  __$UpdatePasswordAuthEventCopyWithImpl(this._self, this._then);

  final _UpdatePasswordAuthEvent _self;
  final $Res Function(_UpdatePasswordAuthEvent) _then;

/// Create a copy of AuthEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? password = null,}) {
  return _then(_UpdatePasswordAuthEvent(
null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _UpdateRePasswordAuthEvent with DiagnosticableTreeMixin implements AuthEvent {
  const _UpdateRePasswordAuthEvent(this.rePassword);
  

 final  String rePassword;

/// Create a copy of AuthEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateRePasswordAuthEventCopyWith<_UpdateRePasswordAuthEvent> get copyWith => __$UpdateRePasswordAuthEventCopyWithImpl<_UpdateRePasswordAuthEvent>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.updateRePassword'))
    ..add(DiagnosticsProperty('rePassword', rePassword));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateRePasswordAuthEvent&&(identical(other.rePassword, rePassword) || other.rePassword == rePassword));
}


@override
int get hashCode => Object.hash(runtimeType,rePassword);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.updateRePassword(rePassword: $rePassword)';
}


}

/// @nodoc
abstract mixin class _$UpdateRePasswordAuthEventCopyWith<$Res> implements $AuthEventCopyWith<$Res> {
  factory _$UpdateRePasswordAuthEventCopyWith(_UpdateRePasswordAuthEvent value, $Res Function(_UpdateRePasswordAuthEvent) _then) = __$UpdateRePasswordAuthEventCopyWithImpl;
@useResult
$Res call({
 String rePassword
});




}
/// @nodoc
class __$UpdateRePasswordAuthEventCopyWithImpl<$Res>
    implements _$UpdateRePasswordAuthEventCopyWith<$Res> {
  __$UpdateRePasswordAuthEventCopyWithImpl(this._self, this._then);

  final _UpdateRePasswordAuthEvent _self;
  final $Res Function(_UpdateRePasswordAuthEvent) _then;

/// Create a copy of AuthEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? rePassword = null,}) {
  return _then(_UpdateRePasswordAuthEvent(
null == rePassword ? _self.rePassword : rePassword // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _SignInAuthEvent with DiagnosticableTreeMixin implements AuthEvent {
  const _SignInAuthEvent();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.signIn'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SignInAuthEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.signIn()';
}


}




/// @nodoc


class _SignUpAuthEvent with DiagnosticableTreeMixin implements AuthEvent {
  const _SignUpAuthEvent();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.signUp'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SignUpAuthEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.signUp()';
}


}




/// @nodoc


class _GoToSignUpAuthEvent with DiagnosticableTreeMixin implements AuthEvent {
  const _GoToSignUpAuthEvent();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.goToSignUp'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToSignUpAuthEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.goToSignUp()';
}


}




/// @nodoc


class _GoToSignInAuthEvent with DiagnosticableTreeMixin implements AuthEvent {
  const _GoToSignInAuthEvent();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.goToSignIn'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToSignInAuthEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.goToSignIn()';
}


}




/// @nodoc


class _ToggleShowPassword with DiagnosticableTreeMixin implements AuthEvent {
  const _ToggleShowPassword();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthEvent.toggleShowPassword'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ToggleShowPassword);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthEvent.toggleShowPassword()';
}


}




/// @nodoc
mixin _$AuthState implements DiagnosticableTreeMixin {

 bool get isSignedIn; bool get isFormValid; Credentials get credentials; ProcessState get status; AuthType get authType; bool get showPassword;
/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthStateCopyWith<AuthState> get copyWith => _$AuthStateCopyWithImpl<AuthState>(this as AuthState, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState'))
    ..add(DiagnosticsProperty('isSignedIn', isSignedIn))..add(DiagnosticsProperty('isFormValid', isFormValid))..add(DiagnosticsProperty('credentials', credentials))..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('authType', authType))..add(DiagnosticsProperty('showPassword', showPassword));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthState&&(identical(other.isSignedIn, isSignedIn) || other.isSignedIn == isSignedIn)&&(identical(other.isFormValid, isFormValid) || other.isFormValid == isFormValid)&&(identical(other.credentials, credentials) || other.credentials == credentials)&&(identical(other.status, status) || other.status == status)&&(identical(other.authType, authType) || other.authType == authType)&&(identical(other.showPassword, showPassword) || other.showPassword == showPassword));
}


@override
int get hashCode => Object.hash(runtimeType,isSignedIn,isFormValid,credentials,status,authType,showPassword);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState(isSignedIn: $isSignedIn, isFormValid: $isFormValid, credentials: $credentials, status: $status, authType: $authType, showPassword: $showPassword)';
}


}

/// @nodoc
abstract mixin class $AuthStateCopyWith<$Res>  {
  factory $AuthStateCopyWith(AuthState value, $Res Function(AuthState) _then) = _$AuthStateCopyWithImpl;
@useResult
$Res call({
 bool isSignedIn, bool isFormValid, Credentials credentials, ProcessState status, AuthType authType, bool showPassword
});


$CredentialsCopyWith<$Res> get credentials;$ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class _$AuthStateCopyWithImpl<$Res>
    implements $AuthStateCopyWith<$Res> {
  _$AuthStateCopyWithImpl(this._self, this._then);

  final AuthState _self;
  final $Res Function(AuthState) _then;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isSignedIn = null,Object? isFormValid = null,Object? credentials = null,Object? status = null,Object? authType = null,Object? showPassword = null,}) {
  return _then(_self.copyWith(
isSignedIn: null == isSignedIn ? _self.isSignedIn : isSignedIn // ignore: cast_nullable_to_non_nullable
as bool,isFormValid: null == isFormValid ? _self.isFormValid : isFormValid // ignore: cast_nullable_to_non_nullable
as bool,credentials: null == credentials ? _self.credentials : credentials // ignore: cast_nullable_to_non_nullable
as Credentials,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,authType: null == authType ? _self.authType : authType // ignore: cast_nullable_to_non_nullable
as AuthType,showPassword: null == showPassword ? _self.showPassword : showPassword // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CredentialsCopyWith<$Res> get credentials {
  
  return $CredentialsCopyWith<$Res>(_self.credentials, (value) {
    return _then(_self.copyWith(credentials: value));
  });
}/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}


/// @nodoc


class _AuthState with DiagnosticableTreeMixin implements AuthState {
  const _AuthState({this.isSignedIn = false, this.isFormValid = false, this.credentials = const Credentials(), this.status = const ProcessState.initial(), this.authType = AuthType.signUp, this.showPassword = false});
  

@override@JsonKey() final  bool isSignedIn;
@override@JsonKey() final  bool isFormValid;
@override@JsonKey() final  Credentials credentials;
@override@JsonKey() final  ProcessState status;
@override@JsonKey() final  AuthType authType;
@override@JsonKey() final  bool showPassword;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AuthStateCopyWith<_AuthState> get copyWith => __$AuthStateCopyWithImpl<_AuthState>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AuthState'))
    ..add(DiagnosticsProperty('isSignedIn', isSignedIn))..add(DiagnosticsProperty('isFormValid', isFormValid))..add(DiagnosticsProperty('credentials', credentials))..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('authType', authType))..add(DiagnosticsProperty('showPassword', showPassword));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AuthState&&(identical(other.isSignedIn, isSignedIn) || other.isSignedIn == isSignedIn)&&(identical(other.isFormValid, isFormValid) || other.isFormValid == isFormValid)&&(identical(other.credentials, credentials) || other.credentials == credentials)&&(identical(other.status, status) || other.status == status)&&(identical(other.authType, authType) || other.authType == authType)&&(identical(other.showPassword, showPassword) || other.showPassword == showPassword));
}


@override
int get hashCode => Object.hash(runtimeType,isSignedIn,isFormValid,credentials,status,authType,showPassword);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AuthState(isSignedIn: $isSignedIn, isFormValid: $isFormValid, credentials: $credentials, status: $status, authType: $authType, showPassword: $showPassword)';
}


}

/// @nodoc
abstract mixin class _$AuthStateCopyWith<$Res> implements $AuthStateCopyWith<$Res> {
  factory _$AuthStateCopyWith(_AuthState value, $Res Function(_AuthState) _then) = __$AuthStateCopyWithImpl;
@override @useResult
$Res call({
 bool isSignedIn, bool isFormValid, Credentials credentials, ProcessState status, AuthType authType, bool showPassword
});


@override $CredentialsCopyWith<$Res> get credentials;@override $ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class __$AuthStateCopyWithImpl<$Res>
    implements _$AuthStateCopyWith<$Res> {
  __$AuthStateCopyWithImpl(this._self, this._then);

  final _AuthState _self;
  final $Res Function(_AuthState) _then;

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isSignedIn = null,Object? isFormValid = null,Object? credentials = null,Object? status = null,Object? authType = null,Object? showPassword = null,}) {
  return _then(_AuthState(
isSignedIn: null == isSignedIn ? _self.isSignedIn : isSignedIn // ignore: cast_nullable_to_non_nullable
as bool,isFormValid: null == isFormValid ? _self.isFormValid : isFormValid // ignore: cast_nullable_to_non_nullable
as bool,credentials: null == credentials ? _self.credentials : credentials // ignore: cast_nullable_to_non_nullable
as Credentials,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,authType: null == authType ? _self.authType : authType // ignore: cast_nullable_to_non_nullable
as AuthType,showPassword: null == showPassword ? _self.showPassword : showPassword // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CredentialsCopyWith<$Res> get credentials {
  
  return $CredentialsCopyWith<$Res>(_self.credentials, (value) {
    return _then(_self.copyWith(credentials: value));
  });
}/// Create a copy of AuthState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}

/// @nodoc
mixin _$Credentials implements DiagnosticableTreeMixin {

 String get email; String get password; String get rePassword;
/// Create a copy of Credentials
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CredentialsCopyWith<Credentials> get copyWith => _$CredentialsCopyWithImpl<Credentials>(this as Credentials, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Credentials'))
    ..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('password', password))..add(DiagnosticsProperty('rePassword', rePassword));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Credentials&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password)&&(identical(other.rePassword, rePassword) || other.rePassword == rePassword));
}


@override
int get hashCode => Object.hash(runtimeType,email,password,rePassword);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Credentials(email: $email, password: $password, rePassword: $rePassword)';
}


}

/// @nodoc
abstract mixin class $CredentialsCopyWith<$Res>  {
  factory $CredentialsCopyWith(Credentials value, $Res Function(Credentials) _then) = _$CredentialsCopyWithImpl;
@useResult
$Res call({
 String email, String password, String rePassword
});




}
/// @nodoc
class _$CredentialsCopyWithImpl<$Res>
    implements $CredentialsCopyWith<$Res> {
  _$CredentialsCopyWithImpl(this._self, this._then);

  final Credentials _self;
  final $Res Function(Credentials) _then;

/// Create a copy of Credentials
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? email = null,Object? password = null,Object? rePassword = null,}) {
  return _then(_self.copyWith(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,rePassword: null == rePassword ? _self.rePassword : rePassword // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _Credentials with DiagnosticableTreeMixin implements Credentials {
  const _Credentials({this.email = '', this.password = '', this.rePassword = ''});
  

@override@JsonKey() final  String email;
@override@JsonKey() final  String password;
@override@JsonKey() final  String rePassword;

/// Create a copy of Credentials
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CredentialsCopyWith<_Credentials> get copyWith => __$CredentialsCopyWithImpl<_Credentials>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Credentials'))
    ..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('password', password))..add(DiagnosticsProperty('rePassword', rePassword));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Credentials&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password)&&(identical(other.rePassword, rePassword) || other.rePassword == rePassword));
}


@override
int get hashCode => Object.hash(runtimeType,email,password,rePassword);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Credentials(email: $email, password: $password, rePassword: $rePassword)';
}


}

/// @nodoc
abstract mixin class _$CredentialsCopyWith<$Res> implements $CredentialsCopyWith<$Res> {
  factory _$CredentialsCopyWith(_Credentials value, $Res Function(_Credentials) _then) = __$CredentialsCopyWithImpl;
@override @useResult
$Res call({
 String email, String password, String rePassword
});




}
/// @nodoc
class __$CredentialsCopyWithImpl<$Res>
    implements _$CredentialsCopyWith<$Res> {
  __$CredentialsCopyWithImpl(this._self, this._then);

  final _Credentials _self;
  final $Res Function(_Credentials) _then;

/// Create a copy of Credentials
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? email = null,Object? password = null,Object? rePassword = null,}) {
  return _then(_Credentials(
email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,rePassword: null == rePassword ? _self.rePassword : rePassword // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
