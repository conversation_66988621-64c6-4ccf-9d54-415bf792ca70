part of 'auth_bloc.dart';

@freezed
abstract class AuthEvent with _$AuthEvent {
  const factory AuthEvent.load(AuthType authType) = _LoadAuthEvent;
  const factory AuthEvent.updateEmail(String email) = _UpdateEmailAuthEvent;
  const factory AuthEvent.updatePassword(String password) =
      _UpdatePasswordAuthEvent;
  const factory AuthEvent.updateRePassword(String rePassword) =
      _UpdateRePasswordAuthEvent;

  const factory AuthEvent.signIn() = _SignInAuthEvent;
  const factory AuthEvent.signUp() = _SignUpAuthEvent;

  const factory AuthEvent.goToSignUp() = _GoToSignUpAuthEvent;
  const factory AuthEvent.goToSignIn() = _GoToSignInAuthEvent;

  const factory AuthEvent.toggleShowPassword() = _ToggleShowPassword;
}
