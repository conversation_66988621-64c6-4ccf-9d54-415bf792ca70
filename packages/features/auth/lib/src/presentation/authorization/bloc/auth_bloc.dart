import 'package:auth/src/domain/models/user.dart';
import 'package:auth/src/domain/usecases/get_user_details_usecase.dart';
import 'package:auth/src/domain/usecases/sign_in_usecase.dart';
import 'package:auth/src/domain/usecases/sign_up_usecase.dart';
import 'package:auth/src/navigator/auth_navigator.dart';
import 'package:auth/src/services/domain/usecases/get_current_user_usecase.dart';
import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'auth_bloc.freezed.dart';
part 'auth_event.dart';
part 'auth_state.dart';

@injectable
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc(
    this._signInUsecase,
    this._signUpUsecase,
    this._navigator,
    this._getCurrentUserUsecase,
    @factoryParam AuthType authType,
    this._getUserDetailsUsecase,
  ) : super(const AuthState()) {
    on<AuthEvent>(
      (event, emit) => switch (event) {
        _LoadAuthEvent() => _onLoad(emit, event),
        _UpdateEmailAuthEvent() => _onUpdateEmail(emit, event),
        _UpdatePasswordAuthEvent() => _onUpdatePassword(emit, event),
        _UpdateRePasswordAuthEvent() => _onUpdateRePassword(emit, event),
        _SignInAuthEvent() => _onSignIn(emit, event),
        _SignUpAuthEvent() => _onSignUp(emit, event),
        _GoToSignInAuthEvent() => _onGoToSignIn(emit, event),
        _GoToSignUpAuthEvent() => _onGoToSignUp(emit, event),
        _ToggleShowPassword() => _onToggleShowPassword(emit),
        _ => null,
      },
    );

    add(AuthEvent.load(authType));
  }

  final SignInUsecase _signInUsecase;
  final SignUpUsecase _signUpUsecase;
  final AuthNavigator _navigator;
  final GetCurrentUserUsecase _getCurrentUserUsecase;
  final GetUserDetailsUsecase _getUserDetailsUsecase;

  void _onLoad(
    Emitter<AuthState> emit,
    _LoadAuthEvent event,
  ) {
    emit(state.copyWith(status: const ProcessState.loading()));

    final userId = _getCurrentUserUsecase().getOrNull();

    if (userId.isNotNullOrEmpty) {
      _navigator.pushRegisterUserProfile();
    } else {
      emit(
        state.copyWith(
          authType: event.authType,
          status: const ProcessState.initial(),
        ),
      );
    }
  }

  void _onUpdateEmail(
    Emitter<AuthState> emit,
    _UpdateEmailAuthEvent event,
  ) {
    final credentials = state.credentials.copyWith(email: event.email);
    emit(
      state.copyWith(
        isFormValid: state.isSignUp
            ? credentials.isValidForSignUp()
            : credentials.isValidForSignIn(),
        credentials: credentials,
      ),
    );
  }

  void _onUpdatePassword(
    Emitter<AuthState> emit,
    _UpdatePasswordAuthEvent event,
  ) {
    final credentials = state.credentials.copyWith(password: event.password);
    emit(
      state.copyWith(
        isFormValid: state.isSignUp
            ? credentials.isValidForSignUp()
            : credentials.isValidForSignIn(),
        credentials: credentials,
      ),
    );
  }

  void _onUpdateRePassword(
    Emitter<AuthState> emit,
    _UpdateRePasswordAuthEvent event,
  ) {
    final credentials = state.credentials.copyWith(
      rePassword: event.rePassword,
    );
    emit(
      state.copyWith(
        isFormValid: state.isSignUp
            ? credentials.isValidForSignUp()
            : credentials.isValidForSignIn(),
        credentials: credentials,
      ),
    );
  }

  Future<void> _onSignIn(
    Emitter<AuthState> emit,
    _SignInAuthEvent event,
  ) async {
    if (state.credentials.isValidForSignIn()) {
      emit(state.copyWith(status: const ProcessState.loading()));
      final response = await _signInUsecase(
        email: state.credentials.email,
        password: state.credentials.password,
      );

      response.fold(
        _checkEmailOrValidate,
        (f) => emit(
          state.copyWith(status: ProcessState.error(f)),
        ),
      );
    } else {
      emit(state.copyWith(status: ProcessState.error(Exception())));
    }
  }

  Future<void> _onSignUp(
    Emitter<AuthState> emit,
    _SignUpAuthEvent event,
  ) async {
    if (state.credentials.isValidForSignUp()) {
      emit(state.copyWith(status: const ProcessState.loading()));
      final response = await _signUpUsecase(
        email: state.credentials.email,
        password: state.credentials.password,
      );

      response.fold(
        _checkEmailOrValidate,
        (f) => emit(
          state.copyWith(status: ProcessState.error(f)),
        ),
      );
    } else {
      emit(state.copyWith(status: ProcessState.error(Exception())));
    }
  }

  Future<void> _checkEmailOrValidate(User user) async {
    if (user.userData?.emailVerified ?? false) {
      _navigator.pushRegisterChildren();
    } else {
      await user.userData?.sendEmailVerification();
      _navigator.pushEmailVerification();
    }
    // if (user.userData?.email == '<EMAIL>' ||
    //     user.userData?.email == '<EMAIL>') {
    //   _navigator.pushRegisteredChildrenList();
    //   return;
    // }
    final userId = _getCurrentUserUsecase().getOrNull();

    if (userId.isNotNullOrEmpty) {
      final response = (await _getUserDetailsUsecase(userId!)).getOrNull();
      if (response.isNotNull) {
        _navigator.pushRegisterChildren();
      } else {
        _navigator.pushRegisterUserProfile();
      }
    } else {
      _navigator.pushRegister();
    }
  }

  void _onGoToSignIn(
    Emitter<AuthState> emit,
    _GoToSignInAuthEvent event,
  ) => emit(const AuthState(authType: AuthType.signIn));

  void _onGoToSignUp(
    Emitter<AuthState> emit,
    _GoToSignUpAuthEvent event,
  ) => emit(const AuthState());

  void _onToggleShowPassword(Emitter<AuthState> emit) {
    emit(state.copyWith(showPassword: !state.showPassword));
  }
}
