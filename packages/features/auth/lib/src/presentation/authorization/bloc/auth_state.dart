part of 'auth_bloc.dart';

@freezed
abstract class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool isSignedIn,
    @Default(false) bool isFormValid,
    @Default(Credentials()) Credentials credentials,
    @Default(ProcessState.initial()) ProcessState status,
    @Default(AuthType.signUp) AuthType authType,
    @Default(false) bool showPassword,
  }) = _AuthState;
}

@freezed
abstract class Credentials with _$Credentials {
  const factory Credentials({
    @Default('') String email,
    @Default('') String password,
    @Default('') String rePassword,
  }) = _Credentials;
}

extension on Credentials {
  bool isValidForSignUp() =>
      isValidEmail() &&
      (password.isNotEmpty && rePassword.isNotEmpty && password == rePassword);

  bool isValidForSignIn() =>
      (email.isNotEmpty && emailRegExp.hasMatch(email)) &&
      (password.isNotEmpty);

  bool isValidEmail() => (email.isNotEmpty && emailRegExp.hasMatch(email));
}

extension AuthStateX on AuthState {
  bool get isSignUp => authType == AuthType.signUp;
  bool get isSignIn => authType == AuthType.signIn;
}

final emailRegExp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');

enum AuthType { signIn, signUp }
