import 'package:app_di/app_di.dart';
import 'package:auth/src/presentation/authorization/bloc/auth_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';
import 'package:url_launcher/url_launcher.dart';

class AuthPage extends StatelessWidget {
  const AuthPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => diContainer<AuthBloc>(
        parameter: AuthType.signUp,
      ),
      child: Scaffold(
        bottomSheet: const _BottomButtonsSheet(),
        body: GestureDetector(
          onTap: kIsWeb
              ? null
              : () => FocusScope.of(context).requestFocus(FocusNode()),
          child: SafeArea(
            child: Padding(
              padding: dimen.all.xs,
              child: _SignUpContent(),
            ),
          ),
        ),
      ),
    );
  }
}

class _SignUpContent extends StatelessWidget {
  _SignUpContent();

  final _emailController = TextEditingController(text: '');
  final _passController = TextEditingController(text: '');
  final _rePassController = TextEditingController(text: '');

  String? validateEmail(String? email) => emailRegExp.hasMatch(email ?? '')
      ? null
      : 'Correo electrónico incorrecto';

  String? validatePassword(String? password) =>
      password.isNotNullOrEmpty && password!.length >= 6
      ? null
      : 'La contraseña debe tener 6 o más caracteres alfanuméricos';

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    final bloc = context.read<AuthBloc>();
    return BlocConsumer<AuthBloc, AuthState>(
      listenWhen: (previous, current) =>
          previous.status.isError != current.status.isError,
      listener: (context, state) {
        if (state.status.isError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              showCloseIcon: true,
              behavior: SnackBarBehavior.floating,
              backgroundColor: theme.colorScheme.error,
              content: const Text('Ha ocurrido un error. Intente nuevamente.'),
            ),
          );
        }
      },
      buildWhen: (prev, current) =>
          (prev.authType != current.authType) ||
          (prev.isFormValid != current.isFormValid) ||
          (prev.status != current.status) ||
          (prev.showPassword != current.showPassword),
      builder: (context, state) => state.status.isLoading
          ? const LoadingView()
          : Form(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          state.isSignUp ? 'Registro' : 'Inicio',
                          style: theme.appTextTheme.headline2,
                        ),
                      ],
                    ),
                    const SizedBox(height: nano),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Comedor Zalaeta',
                          style: theme.appTextTheme.headline6,
                        ),
                      ],
                    ),
                    const SizedBox(height: nano),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '(2024-2025)',
                          style: theme.appTextTheme.body2,
                        ),
                      ],
                    ),
                    const SizedBox(height: md),
                    Text(
                      'Correo electrónico',
                      style: theme.appTextTheme.headline5,
                    ),
                    const SizedBox(height: xxs),
                    TextFormField(
                      controller: _emailController
                        ..text = state.credentials.email,
                      validator: validateEmail,
                      enabled: !state.status.isLoading,
                      autocorrect: false,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      onChanged: (text) => bloc.add(
                        AuthEvent.updateEmail(text),
                      ),
                    ),
                    const SizedBox(height: xs),
                    Text(
                      'Contraseña',
                      style: theme.appTextTheme.headline5,
                    ),
                    const SizedBox(height: xxs),
                    TextFormField(
                      obscureText: !state.showPassword,
                      enabled: !state.status.isLoading,
                      controller: _passController
                        ..text = state.credentials.password,
                      validator: validatePassword,
                      decoration: InputDecoration(
                        suffixIcon: IconButton(
                          onPressed: () => bloc.add(
                            const AuthEvent.toggleShowPassword(),
                          ),
                          icon: const Icon(Icons.remove_red_eye_outlined),
                        ),
                      ),
                      textInputAction: TextInputAction.next,
                      onChanged: (text) => bloc.add(
                        AuthEvent.updatePassword(text),
                      ),
                    ),
                    if (state.isSignUp) ...[
                      const SizedBox(height: xs),
                      Text(
                        'Repita contraseña',
                        style: theme.appTextTheme.headline5,
                      ),
                      const SizedBox(height: xxs),
                      TextFormField(
                        obscureText: !state.showPassword,
                        enabled: !state.status.isLoading,
                        controller: _rePassController
                          ..text = state.credentials.rePassword,
                        validator: validatePassword,
                        decoration: InputDecoration(
                          suffixIcon: IconButton(
                            onPressed: () => bloc.add(
                              const AuthEvent.toggleShowPassword(),
                            ),
                            icon: const Icon(Icons.remove_red_eye_outlined),
                          ),
                        ),
                        textInputAction: TextInputAction.done,
                        onChanged: (text) => bloc.add(
                          AuthEvent.updateRePassword(text),
                        ),
                      ),
                      const SizedBox(height: xs),
                      RichText(
                        text: TextSpan(
                          text:
                              'Al registrarse usted declara haber leído y aceptado nuestra ',
                          style: context.appTheme.appTextTheme.body1,
                          children: [
                            TextSpan(
                              text: 'política de privacidad.',
                              recognizer: TapGestureRecognizer()
                                ..onTap = () async {},
                              style: context.appTheme.appTextTheme.body1
                                  .copyWith(
                                    color: context.appTheme.colorScheme.primary,
                                    decoration: TextDecoration.underline,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: xs),
                      Text(
                        'Para que el alta, baja o modificación de servicios de comedor sea válida debe cumplir todos los requisitos de admisión. Su incumplimiento puede determinar la falta de efectos de la solicitud, sin que sea preciso requerimiento de subsanación. Si fuera el caso, se podría tomar como fecha de solicitud cuando se cumplan todos los requisitos.',
                        style: context.appTheme.appTextTheme.body3,
                      ),
                      const SizedBox(height: xs),
                      Text(
                        'El proceso de alta debe ser antes del día 31 de agosto para que sea válido el mes de septiembre.',
                        style: context.appTheme.appTextTheme.body3,
                      ),
                      const SizedBox(height: xs),
                      RichText(
                        text: TextSpan(
                          text:
                              'Para cualquier tipo de duda, queja o sugerencia acerca de este sistema puede escribir por WhatsApp al siguiente número: ',
                          style: context.appTheme.appTextTheme.body3,
                          children: [
                            TextSpan(
                              text: '+34 643 576-455',
                              recognizer: TapGestureRecognizer()
                                ..onTap = () async {
                                  launchUrl(
                                    Uri.parse('https://wa.me/34643576455'),
                                  );
                                },
                              style: context.appTheme.appTextTheme.body3
                                  .copyWith(
                                    color: context.appTheme.colorScheme.primary,
                                    decoration: TextDecoration.underline,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: xxxl * 3),
                    ],
                  ],
                ),
              ),
            ),
    );
  }
}

class _BottomButtonsSheet extends StatelessWidget {
  const _BottomButtonsSheet();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return SafeArea(
          child: Padding(
            padding: dimen.all.xs,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                PrimaryButton.responsive(
                  title: state.isSignUp ? 'Registrarse' : 'Iniciar',
                  isLoading: state.status.isLoading,
                  onPressed: state.isFormValid
                      ? () => context.read<AuthBloc>().add(
                          state.isSignUp
                              ? const AuthEvent.signUp()
                              : const AuthEvent.signIn(),
                        )
                      : null,
                ),
                const SizedBox(height: xxs).center,
                TertiaryButton.responsive(
                  title: state.isSignUp
                      ? '¿Tienes cuenta? Ir a Iniciar'
                      : '¿No tienes cuenta? Ir al Registro',
                  onPressed: () => context.read<AuthBloc>().add(
                    state.isSignUp
                        ? const AuthEvent.goToSignIn()
                        : const AuthEvent.goToSignUp(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class LoadingView extends StatelessWidget {
  const LoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox.expand(
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
