import 'package:auth/src/domain/models/user_details.dart';
import 'package:auth/src/domain/usecases/save_user_details_usecase.dart';
import 'package:auth/src/navigator/auth_navigator.dart';
import 'package:auth/src/services/domain/usecases/get_current_user_usecase.dart';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'register_user_profile_bloc.freezed.dart';
part 'register_user_profile_event.dart';
part 'register_user_profile_state.dart';

@injectable
class RegisterUserProfileBloc
    extends Bloc<RegisterUserProfileEvent, RegisterUserProfileState> {
  RegisterUserProfileBloc(
    this._getCurrentUserUsecase,
    this._saveUserDetailsUsecase,
    this._navigator,
  ) : super(const _RegisterUserProfileState()) {
    on<RegisterUserProfileEvent>(
      (event, emit) => switch (event) {
        _LoadRegisterUserProfileEvent() => _onLoad(emit, event),
        _ChangeFieldRegisterUserProfileEvent() => _onChangeField(emit, event),
        _SendRegisterUserProfileEvent() => _onSend(emit, event),
        _ => null,
      },
    );

    add(const RegisterUserProfileEvent.load());
  }

  final GetCurrentUserUsecase _getCurrentUserUsecase;
  final SaveUserDetailsUsecase _saveUserDetailsUsecase;
  final AuthNavigator _navigator;

  Future<void> _onLoad(
    Emitter<RegisterUserProfileState> emit,
    _LoadRegisterUserProfileEvent event,
  ) async {
    final userId = _getCurrentUserUsecase();
    userId.fold((_) {}, (f) => _navigator.pushRegister());
  }

  void _onChangeField(
    Emitter<RegisterUserProfileState> emit,
    _ChangeFieldRegisterUserProfileEvent event,
  ) {
    final profile = state.userProfile.copyWith(
      name: event.name ?? state.userProfile.name,
      lastName: event.lastName ?? state.userProfile.lastName,
      secondLastName: event.secondLastName ?? state.userProfile.secondLastName,
      identification: event.identification ?? state.userProfile.identification,
      bankAccount: event.bankAccount ?? state.userProfile.bankAccount,
      phone: event.phone ?? state.userProfile.phone,
      emergencyContact:
          event.emergencyContact ?? state.userProfile.emergencyContact,
      isAnpa: event.isAnpa ?? state.userProfile.isAnpa,
      ibanFilled: event.ibanFilled ?? state.userProfile.ibanFilled,
    );

    emit(
      state.copyWith(
        isValid: profile.isValid(),
        userProfile: profile,
      ),
    );
  }

  Future<void> _onSend(
    Emitter<RegisterUserProfileState> emit,
    _SendRegisterUserProfileEvent event,
  ) async {
    emit(state.copyWith(status: const ProcessState.loading()));

    final userId = _getCurrentUserUsecase().getOrNull();
    if (state.isValid && userId.isNotNullOrEmpty) {
      final response = await _saveUserDetailsUsecase(
        userId!,
        state.userProfile.toModel(),
      );

      response.fold(
        (_) => _navigator.pushRegisterChildren(),
        (f) => emit(
          state.copyWith(status: ProcessState.error(f)),
        ),
      );
    } else {
      emit(
        state.copyWith(status: ProcessState.error(Exception())),
      );
    }
  }
}
