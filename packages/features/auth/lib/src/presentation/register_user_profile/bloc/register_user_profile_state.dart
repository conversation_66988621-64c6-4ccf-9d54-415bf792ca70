part of 'register_user_profile_bloc.dart';

@freezed
abstract class RegisterUserProfileState with _$RegisterUserProfileState {
  const factory RegisterUserProfileState({
    @Default(ProcessState.initial()) ProcessState status,
    @Default(TempUserProfile()) TempUserProfile userProfile,
    @Default(false) bool isValid,
  }) = _RegisterUserProfileState;
}

@freezed
abstract class TempUserProfile with _$TempUserProfile {
  const factory TempUserProfile({
    String? name,
    String? lastName,
    String? secondLastName,
    String? identification,
    String? bankAccount,
    String? phone,
    String? emergencyContact,
    bool? ibanFilled,
    bool? isAnpa,
  }) = _TempUserProfile;
}

extension on TempUserProfile {
  bool isValid() =>
      name.isNotNullOrEmpty &&
      lastName.isNotNullOrEmpty &&
      secondLastName.isNotNullOrEmpty &&
      (identification.isNotNullOrEmpty && identification?.length == 9) &&
      (bankAccount.isNotNullOrEmpty && bankAccount?.length == 24) &&
      phone.isNotNullOrEmpty &&
      (ibanFilled ?? false);

  UserDetails toModel() => UserDetails(
    name: name!,
    lastName: lastName!,
    secondLastName: secondLastName!,
    identification: identification!,
    bankAccount: bankAccount!,
    phone: phone!,
    emergencyContact: emergencyContact,
    isAnpa: isAnpa!,
  );
}
