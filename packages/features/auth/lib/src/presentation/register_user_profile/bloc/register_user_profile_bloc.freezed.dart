// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_user_profile_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$RegisterUserProfileEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisterUserProfileEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RegisterUserProfileEvent()';
}


}

/// @nodoc
class $RegisterUserProfileEventCopyWith<$Res>  {
$RegisterUserProfileEventCopyWith(RegisterUserProfileEvent _, $Res Function(RegisterUserProfileEvent) __);
}


/// @nodoc


class _LoadRegisterUserProfileEvent implements RegisterUserProfileEvent {
  const _LoadRegisterUserProfileEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadRegisterUserProfileEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RegisterUserProfileEvent.load()';
}


}




/// @nodoc


class _ChangeFieldRegisterUserProfileEvent implements RegisterUserProfileEvent {
  const _ChangeFieldRegisterUserProfileEvent({this.name, this.lastName, this.secondLastName, this.identification, this.bankAccount, this.phone, this.emergencyContact, this.ibanFilled, this.isAnpa});
  

 final  String? name;
 final  String? lastName;
 final  String? secondLastName;
 final  String? identification;
 final  String? bankAccount;
 final  String? phone;
 final  String? emergencyContact;
 final  bool? ibanFilled;
 final  bool? isAnpa;

/// Create a copy of RegisterUserProfileEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeFieldRegisterUserProfileEventCopyWith<_ChangeFieldRegisterUserProfileEvent> get copyWith => __$ChangeFieldRegisterUserProfileEventCopyWithImpl<_ChangeFieldRegisterUserProfileEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeFieldRegisterUserProfileEvent&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.secondLastName, secondLastName) || other.secondLastName == secondLastName)&&(identical(other.identification, identification) || other.identification == identification)&&(identical(other.bankAccount, bankAccount) || other.bankAccount == bankAccount)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.emergencyContact, emergencyContact) || other.emergencyContact == emergencyContact)&&(identical(other.ibanFilled, ibanFilled) || other.ibanFilled == ibanFilled)&&(identical(other.isAnpa, isAnpa) || other.isAnpa == isAnpa));
}


@override
int get hashCode => Object.hash(runtimeType,name,lastName,secondLastName,identification,bankAccount,phone,emergencyContact,ibanFilled,isAnpa);

@override
String toString() {
  return 'RegisterUserProfileEvent.changeField(name: $name, lastName: $lastName, secondLastName: $secondLastName, identification: $identification, bankAccount: $bankAccount, phone: $phone, emergencyContact: $emergencyContact, ibanFilled: $ibanFilled, isAnpa: $isAnpa)';
}


}

/// @nodoc
abstract mixin class _$ChangeFieldRegisterUserProfileEventCopyWith<$Res> implements $RegisterUserProfileEventCopyWith<$Res> {
  factory _$ChangeFieldRegisterUserProfileEventCopyWith(_ChangeFieldRegisterUserProfileEvent value, $Res Function(_ChangeFieldRegisterUserProfileEvent) _then) = __$ChangeFieldRegisterUserProfileEventCopyWithImpl;
@useResult
$Res call({
 String? name, String? lastName, String? secondLastName, String? identification, String? bankAccount, String? phone, String? emergencyContact, bool? ibanFilled, bool? isAnpa
});




}
/// @nodoc
class __$ChangeFieldRegisterUserProfileEventCopyWithImpl<$Res>
    implements _$ChangeFieldRegisterUserProfileEventCopyWith<$Res> {
  __$ChangeFieldRegisterUserProfileEventCopyWithImpl(this._self, this._then);

  final _ChangeFieldRegisterUserProfileEvent _self;
  final $Res Function(_ChangeFieldRegisterUserProfileEvent) _then;

/// Create a copy of RegisterUserProfileEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? lastName = freezed,Object? secondLastName = freezed,Object? identification = freezed,Object? bankAccount = freezed,Object? phone = freezed,Object? emergencyContact = freezed,Object? ibanFilled = freezed,Object? isAnpa = freezed,}) {
  return _then(_ChangeFieldRegisterUserProfileEvent(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,secondLastName: freezed == secondLastName ? _self.secondLastName : secondLastName // ignore: cast_nullable_to_non_nullable
as String?,identification: freezed == identification ? _self.identification : identification // ignore: cast_nullable_to_non_nullable
as String?,bankAccount: freezed == bankAccount ? _self.bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,emergencyContact: freezed == emergencyContact ? _self.emergencyContact : emergencyContact // ignore: cast_nullable_to_non_nullable
as String?,ibanFilled: freezed == ibanFilled ? _self.ibanFilled : ibanFilled // ignore: cast_nullable_to_non_nullable
as bool?,isAnpa: freezed == isAnpa ? _self.isAnpa : isAnpa // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

/// @nodoc


class _SendRegisterUserProfileEvent implements RegisterUserProfileEvent {
  const _SendRegisterUserProfileEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SendRegisterUserProfileEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RegisterUserProfileEvent.sendUserProfile()';
}


}




/// @nodoc
mixin _$RegisterUserProfileState {

 ProcessState get status; TempUserProfile get userProfile; bool get isValid;
/// Create a copy of RegisterUserProfileState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RegisterUserProfileStateCopyWith<RegisterUserProfileState> get copyWith => _$RegisterUserProfileStateCopyWithImpl<RegisterUserProfileState>(this as RegisterUserProfileState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisterUserProfileState&&(identical(other.status, status) || other.status == status)&&(identical(other.userProfile, userProfile) || other.userProfile == userProfile)&&(identical(other.isValid, isValid) || other.isValid == isValid));
}


@override
int get hashCode => Object.hash(runtimeType,status,userProfile,isValid);

@override
String toString() {
  return 'RegisterUserProfileState(status: $status, userProfile: $userProfile, isValid: $isValid)';
}


}

/// @nodoc
abstract mixin class $RegisterUserProfileStateCopyWith<$Res>  {
  factory $RegisterUserProfileStateCopyWith(RegisterUserProfileState value, $Res Function(RegisterUserProfileState) _then) = _$RegisterUserProfileStateCopyWithImpl;
@useResult
$Res call({
 ProcessState status, TempUserProfile userProfile, bool isValid
});


$ProcessStateCopyWith<$Res> get status;$TempUserProfileCopyWith<$Res> get userProfile;

}
/// @nodoc
class _$RegisterUserProfileStateCopyWithImpl<$Res>
    implements $RegisterUserProfileStateCopyWith<$Res> {
  _$RegisterUserProfileStateCopyWithImpl(this._self, this._then);

  final RegisterUserProfileState _self;
  final $Res Function(RegisterUserProfileState) _then;

/// Create a copy of RegisterUserProfileState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? userProfile = null,Object? isValid = null,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,userProfile: null == userProfile ? _self.userProfile : userProfile // ignore: cast_nullable_to_non_nullable
as TempUserProfile,isValid: null == isValid ? _self.isValid : isValid // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}
/// Create a copy of RegisterUserProfileState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}/// Create a copy of RegisterUserProfileState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TempUserProfileCopyWith<$Res> get userProfile {
  
  return $TempUserProfileCopyWith<$Res>(_self.userProfile, (value) {
    return _then(_self.copyWith(userProfile: value));
  });
}
}


/// @nodoc


class _RegisterUserProfileState implements RegisterUserProfileState {
  const _RegisterUserProfileState({this.status = const ProcessState.initial(), this.userProfile = const TempUserProfile(), this.isValid = false});
  

@override@JsonKey() final  ProcessState status;
@override@JsonKey() final  TempUserProfile userProfile;
@override@JsonKey() final  bool isValid;

/// Create a copy of RegisterUserProfileState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RegisterUserProfileStateCopyWith<_RegisterUserProfileState> get copyWith => __$RegisterUserProfileStateCopyWithImpl<_RegisterUserProfileState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RegisterUserProfileState&&(identical(other.status, status) || other.status == status)&&(identical(other.userProfile, userProfile) || other.userProfile == userProfile)&&(identical(other.isValid, isValid) || other.isValid == isValid));
}


@override
int get hashCode => Object.hash(runtimeType,status,userProfile,isValid);

@override
String toString() {
  return 'RegisterUserProfileState(status: $status, userProfile: $userProfile, isValid: $isValid)';
}


}

/// @nodoc
abstract mixin class _$RegisterUserProfileStateCopyWith<$Res> implements $RegisterUserProfileStateCopyWith<$Res> {
  factory _$RegisterUserProfileStateCopyWith(_RegisterUserProfileState value, $Res Function(_RegisterUserProfileState) _then) = __$RegisterUserProfileStateCopyWithImpl;
@override @useResult
$Res call({
 ProcessState status, TempUserProfile userProfile, bool isValid
});


@override $ProcessStateCopyWith<$Res> get status;@override $TempUserProfileCopyWith<$Res> get userProfile;

}
/// @nodoc
class __$RegisterUserProfileStateCopyWithImpl<$Res>
    implements _$RegisterUserProfileStateCopyWith<$Res> {
  __$RegisterUserProfileStateCopyWithImpl(this._self, this._then);

  final _RegisterUserProfileState _self;
  final $Res Function(_RegisterUserProfileState) _then;

/// Create a copy of RegisterUserProfileState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? userProfile = null,Object? isValid = null,}) {
  return _then(_RegisterUserProfileState(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,userProfile: null == userProfile ? _self.userProfile : userProfile // ignore: cast_nullable_to_non_nullable
as TempUserProfile,isValid: null == isValid ? _self.isValid : isValid // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of RegisterUserProfileState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}/// Create a copy of RegisterUserProfileState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TempUserProfileCopyWith<$Res> get userProfile {
  
  return $TempUserProfileCopyWith<$Res>(_self.userProfile, (value) {
    return _then(_self.copyWith(userProfile: value));
  });
}
}

/// @nodoc
mixin _$TempUserProfile {

 String? get name; String? get lastName; String? get secondLastName; String? get identification; String? get bankAccount; String? get phone; String? get emergencyContact; bool? get ibanFilled; bool? get isAnpa;
/// Create a copy of TempUserProfile
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempUserProfileCopyWith<TempUserProfile> get copyWith => _$TempUserProfileCopyWithImpl<TempUserProfile>(this as TempUserProfile, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempUserProfile&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.secondLastName, secondLastName) || other.secondLastName == secondLastName)&&(identical(other.identification, identification) || other.identification == identification)&&(identical(other.bankAccount, bankAccount) || other.bankAccount == bankAccount)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.emergencyContact, emergencyContact) || other.emergencyContact == emergencyContact)&&(identical(other.ibanFilled, ibanFilled) || other.ibanFilled == ibanFilled)&&(identical(other.isAnpa, isAnpa) || other.isAnpa == isAnpa));
}


@override
int get hashCode => Object.hash(runtimeType,name,lastName,secondLastName,identification,bankAccount,phone,emergencyContact,ibanFilled,isAnpa);

@override
String toString() {
  return 'TempUserProfile(name: $name, lastName: $lastName, secondLastName: $secondLastName, identification: $identification, bankAccount: $bankAccount, phone: $phone, emergencyContact: $emergencyContact, ibanFilled: $ibanFilled, isAnpa: $isAnpa)';
}


}

/// @nodoc
abstract mixin class $TempUserProfileCopyWith<$Res>  {
  factory $TempUserProfileCopyWith(TempUserProfile value, $Res Function(TempUserProfile) _then) = _$TempUserProfileCopyWithImpl;
@useResult
$Res call({
 String? name, String? lastName, String? secondLastName, String? identification, String? bankAccount, String? phone, String? emergencyContact, bool? ibanFilled, bool? isAnpa
});




}
/// @nodoc
class _$TempUserProfileCopyWithImpl<$Res>
    implements $TempUserProfileCopyWith<$Res> {
  _$TempUserProfileCopyWithImpl(this._self, this._then);

  final TempUserProfile _self;
  final $Res Function(TempUserProfile) _then;

/// Create a copy of TempUserProfile
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,Object? lastName = freezed,Object? secondLastName = freezed,Object? identification = freezed,Object? bankAccount = freezed,Object? phone = freezed,Object? emergencyContact = freezed,Object? ibanFilled = freezed,Object? isAnpa = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,secondLastName: freezed == secondLastName ? _self.secondLastName : secondLastName // ignore: cast_nullable_to_non_nullable
as String?,identification: freezed == identification ? _self.identification : identification // ignore: cast_nullable_to_non_nullable
as String?,bankAccount: freezed == bankAccount ? _self.bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,emergencyContact: freezed == emergencyContact ? _self.emergencyContact : emergencyContact // ignore: cast_nullable_to_non_nullable
as String?,ibanFilled: freezed == ibanFilled ? _self.ibanFilled : ibanFilled // ignore: cast_nullable_to_non_nullable
as bool?,isAnpa: freezed == isAnpa ? _self.isAnpa : isAnpa // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

}


/// @nodoc


class _TempUserProfile implements TempUserProfile {
  const _TempUserProfile({this.name, this.lastName, this.secondLastName, this.identification, this.bankAccount, this.phone, this.emergencyContact, this.ibanFilled, this.isAnpa});
  

@override final  String? name;
@override final  String? lastName;
@override final  String? secondLastName;
@override final  String? identification;
@override final  String? bankAccount;
@override final  String? phone;
@override final  String? emergencyContact;
@override final  bool? ibanFilled;
@override final  bool? isAnpa;

/// Create a copy of TempUserProfile
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempUserProfileCopyWith<_TempUserProfile> get copyWith => __$TempUserProfileCopyWithImpl<_TempUserProfile>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempUserProfile&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.secondLastName, secondLastName) || other.secondLastName == secondLastName)&&(identical(other.identification, identification) || other.identification == identification)&&(identical(other.bankAccount, bankAccount) || other.bankAccount == bankAccount)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.emergencyContact, emergencyContact) || other.emergencyContact == emergencyContact)&&(identical(other.ibanFilled, ibanFilled) || other.ibanFilled == ibanFilled)&&(identical(other.isAnpa, isAnpa) || other.isAnpa == isAnpa));
}


@override
int get hashCode => Object.hash(runtimeType,name,lastName,secondLastName,identification,bankAccount,phone,emergencyContact,ibanFilled,isAnpa);

@override
String toString() {
  return 'TempUserProfile(name: $name, lastName: $lastName, secondLastName: $secondLastName, identification: $identification, bankAccount: $bankAccount, phone: $phone, emergencyContact: $emergencyContact, ibanFilled: $ibanFilled, isAnpa: $isAnpa)';
}


}

/// @nodoc
abstract mixin class _$TempUserProfileCopyWith<$Res> implements $TempUserProfileCopyWith<$Res> {
  factory _$TempUserProfileCopyWith(_TempUserProfile value, $Res Function(_TempUserProfile) _then) = __$TempUserProfileCopyWithImpl;
@override @useResult
$Res call({
 String? name, String? lastName, String? secondLastName, String? identification, String? bankAccount, String? phone, String? emergencyContact, bool? ibanFilled, bool? isAnpa
});




}
/// @nodoc
class __$TempUserProfileCopyWithImpl<$Res>
    implements _$TempUserProfileCopyWith<$Res> {
  __$TempUserProfileCopyWithImpl(this._self, this._then);

  final _TempUserProfile _self;
  final $Res Function(_TempUserProfile) _then;

/// Create a copy of TempUserProfile
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? lastName = freezed,Object? secondLastName = freezed,Object? identification = freezed,Object? bankAccount = freezed,Object? phone = freezed,Object? emergencyContact = freezed,Object? ibanFilled = freezed,Object? isAnpa = freezed,}) {
  return _then(_TempUserProfile(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,secondLastName: freezed == secondLastName ? _self.secondLastName : secondLastName // ignore: cast_nullable_to_non_nullable
as String?,identification: freezed == identification ? _self.identification : identification // ignore: cast_nullable_to_non_nullable
as String?,bankAccount: freezed == bankAccount ? _self.bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,emergencyContact: freezed == emergencyContact ? _self.emergencyContact : emergencyContact // ignore: cast_nullable_to_non_nullable
as String?,ibanFilled: freezed == ibanFilled ? _self.ibanFilled : ibanFilled // ignore: cast_nullable_to_non_nullable
as bool?,isAnpa: freezed == isAnpa ? _self.isAnpa : isAnpa // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

// dart format on
