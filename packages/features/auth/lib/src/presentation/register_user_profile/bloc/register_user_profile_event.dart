part of 'register_user_profile_bloc.dart';

@freezed
abstract class RegisterUserProfileEvent with _$RegisterUserProfileEvent {
  const factory RegisterUserProfileEvent.load() = _LoadRegisterUserProfileEvent;

  const factory RegisterUserProfileEvent.changeField({
    String? name,
    String? lastName,
    String? secondLastName,
    String? identification,
    String? bankAccount,
    String? phone,
    String? emergencyContact,
    bool? ibanFilled,
    bool? isAnpa,
  }) = _ChangeFieldRegisterUserProfileEvent;

  const factory RegisterUserProfileEvent.sendUserProfile() =
      _SendRegisterUserProfileEvent;
}
