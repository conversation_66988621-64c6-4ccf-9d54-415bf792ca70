import 'package:auth/src/presentation/register_user_profile/bloc/register_user_profile_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:prelude/prelude.dart';

class RegisterUserProfilePage extends StatelessWidget {
  const RegisterUserProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance.get<RegisterUserProfileBloc>(),
      child: Scaffold(
        bottomSheet: const _BottomButtonsSheet(),
        body: GestureDetector(
          onTap: kIsWeb
              ? null
              : () => FocusScope.of(context).requestFocus(FocusNode()),
          child: SafeArea(
            child: Padding(
              padding: dimen.all.xs,
              child: const _Content(),
            ),
          ),
        ),
      ),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  String? _mandatoryFieldValidation(String? value) =>
      value.isNotNullOrEmpty ? null : 'Este campo es obligatorio';

  String? _dniFieldValidation(String? value) {
    if (value.isNotNullOrEmpty && value?.length == 9) {
      return null;
    } else {
      return 'Campo incorrecto';
    }
  }

  String? _ibanFiledValidation(String? value) {
    if (value.isNotNullOrEmpty && value?.length == 24) {
      return null;
    } else {
      return 'Campo incorrecto';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    final bloc = context.read<RegisterUserProfileBloc>();
    return BlocConsumer<RegisterUserProfileBloc, RegisterUserProfileState>(
      listenWhen: (previous, current) =>
          previous.status.isError != current.status.isError,
      listener: (context, state) {
        if (state.status.isError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              showCloseIcon: true,
              behavior: SnackBarBehavior.floating,
              backgroundColor: theme.colorScheme.error,
              content: const Text('Ha ocurrido un error. Intente nuevamente.'),
            ),
          );
        }
      },
      builder: (context, state) => Form(
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Completar Perfil',
                    style: theme.appTextTheme.headline2,
                  ),
                ],
              ),
              const SizedBox(height: md),
              Text(
                'Nombre(s)',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                enabled: !state.status.isLoading,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                initialValue: state.userProfile.name,
                validator: _mandatoryFieldValidation,
                onChanged: (text) => bloc.add(
                  RegisterUserProfileEvent.changeField(name: text),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Primer Apellido',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                textInputAction: TextInputAction.next,
                initialValue: state.userProfile.lastName,
                validator: _mandatoryFieldValidation,
                onChanged: (text) => bloc.add(
                  RegisterUserProfileEvent.changeField(lastName: text),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Segundo Apellido',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                textInputAction: TextInputAction.next,
                initialValue: state.userProfile.secondLastName,
                validator: _mandatoryFieldValidation,
                onChanged: (text) => bloc.add(
                  RegisterUserProfileEvent.changeField(secondLastName: text),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'DNI / NIE',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                textInputAction: TextInputAction.next,
                initialValue: state.userProfile.identification,
                validator: _dniFieldValidation,
                onChanged: (text) => bloc.add(
                  RegisterUserProfileEvent.changeField(identification: text),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Cuenta Bancaria (IBAN)',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                textInputAction: TextInputAction.next,
                initialValue: state.userProfile.bankAccount,
                validator: _ibanFiledValidation,
                onChanged: (text) => bloc.add(
                  RegisterUserProfileEvent.changeField(bankAccount: text),
                ),
              ),
              const SizedBox(height: micro),
              CheckboxListTile(
                title: Text(
                  'Al rellenar este campo usted acepta los recibos de cargo a la cuenta de la que es titular por los Servicios de Comedor en función de la modalidad escogida.',
                  style: theme.appTextTheme.body1,
                ),
                value: state.userProfile.ibanFilled ?? false,
                onChanged: (value) => bloc.add(
                  RegisterUserProfileEvent.changeField(ibanFilled: value),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Teléfono',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                decoration: const InputDecoration(prefix: Text('+34 ')),
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.phone,
                initialValue: state.userProfile.phone,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                ],
                validator: _mandatoryFieldValidation,
                onChanged: (text) => bloc.add(
                  RegisterUserProfileEvent.changeField(phone: text),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Contacto de emergencia (opcional)',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                decoration: const InputDecoration(prefix: Text('+34 ')),
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.phone,
                initialValue: state.userProfile.emergencyContact,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                ],
                onChanged: (text) => bloc.add(
                  RegisterUserProfileEvent.changeField(
                    emergencyContact: text,
                  ),
                ),
              ),
              const SizedBox(height: xs),
              CheckboxListTile(
                title: Text(
                  '¿Es socio ANPA?',
                  style: theme.appTextTheme.body1,
                ),
                value: state.userProfile.isAnpa ?? false,
                onChanged: (value) => bloc.add(
                  RegisterUserProfileEvent.changeField(isAnpa: value),
                ),
              ),
              const SizedBox(height: xxxl * 2),
            ],
          ),
        ),
      ),
    );
  }
}

class _BottomButtonsSheet extends StatelessWidget {
  const _BottomButtonsSheet();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RegisterUserProfileBloc, RegisterUserProfileState>(
      builder: (context, state) {
        return SafeArea(
          child: Padding(
            padding: dimen.all.xs,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                PrimaryButton.responsive(
                  title: 'Continuar',
                  isLoading: state.status.isLoading,
                  onPressed: state.isValid
                      ? () => context.read<RegisterUserProfileBloc>().add(
                          const RegisterUserProfileEvent.sendUserProfile(),
                        )
                      : null,
                ),
                const SizedBox(height: xs),
              ],
            ),
          ),
        );
      },
    );
  }
}
