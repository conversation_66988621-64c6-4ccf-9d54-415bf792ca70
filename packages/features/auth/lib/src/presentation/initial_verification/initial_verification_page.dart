import 'package:app_di/app_di.dart';
import 'package:auth/src/presentation/initial_verification/bloc/initial_verification_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class InitialVerificationPage extends StatelessWidget {
  const InitialVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: BlocProvider(
          create: (context) => diContainer<InitialVerificationBloc>(),
          child: BlocBuilder<InitialVerificationBloc, InitialVerificationState>(
            builder: (context, state) {
              return const CircularProgressIndicator();
            },
          ),
        ),
      ),
    );
  }
}
