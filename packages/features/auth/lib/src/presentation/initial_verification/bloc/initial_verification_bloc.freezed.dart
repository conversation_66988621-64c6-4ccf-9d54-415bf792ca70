// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'initial_verification_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$InitialVerificationEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InitialVerificationEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'InitialVerificationEvent()';
}


}

/// @nodoc
class $InitialVerificationEventCopyWith<$Res>  {
$InitialVerificationEventCopyWith(InitialVerificationEvent _, $Res Function(InitialVerificationEvent) __);
}


/// @nodoc


class _Started implements InitialVerificationEvent {
  const _Started();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Started);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'InitialVerificationEvent.started()';
}


}




/// @nodoc
mixin _$InitialVerificationState {

 ProcessState get status;
/// Create a copy of InitialVerificationState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InitialVerificationStateCopyWith<InitialVerificationState> get copyWith => _$InitialVerificationStateCopyWithImpl<InitialVerificationState>(this as InitialVerificationState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InitialVerificationState&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,status);

@override
String toString() {
  return 'InitialVerificationState(status: $status)';
}


}

/// @nodoc
abstract mixin class $InitialVerificationStateCopyWith<$Res>  {
  factory $InitialVerificationStateCopyWith(InitialVerificationState value, $Res Function(InitialVerificationState) _then) = _$InitialVerificationStateCopyWithImpl;
@useResult
$Res call({
 ProcessState status
});


$ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class _$InitialVerificationStateCopyWithImpl<$Res>
    implements $InitialVerificationStateCopyWith<$Res> {
  _$InitialVerificationStateCopyWithImpl(this._self, this._then);

  final InitialVerificationState _self;
  final $Res Function(InitialVerificationState) _then;

/// Create a copy of InitialVerificationState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,
  ));
}
/// Create a copy of InitialVerificationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}


/// @nodoc


class _LoadInitialVerificationState implements InitialVerificationState {
  const _LoadInitialVerificationState({this.status = const ProcessState.initial()});
  

@override@JsonKey() final  ProcessState status;

/// Create a copy of InitialVerificationState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoadInitialVerificationStateCopyWith<_LoadInitialVerificationState> get copyWith => __$LoadInitialVerificationStateCopyWithImpl<_LoadInitialVerificationState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadInitialVerificationState&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,status);

@override
String toString() {
  return 'InitialVerificationState(status: $status)';
}


}

/// @nodoc
abstract mixin class _$LoadInitialVerificationStateCopyWith<$Res> implements $InitialVerificationStateCopyWith<$Res> {
  factory _$LoadInitialVerificationStateCopyWith(_LoadInitialVerificationState value, $Res Function(_LoadInitialVerificationState) _then) = __$LoadInitialVerificationStateCopyWithImpl;
@override @useResult
$Res call({
 ProcessState status
});


@override $ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class __$LoadInitialVerificationStateCopyWithImpl<$Res>
    implements _$LoadInitialVerificationStateCopyWith<$Res> {
  __$LoadInitialVerificationStateCopyWithImpl(this._self, this._then);

  final _LoadInitialVerificationState _self;
  final $Res Function(_LoadInitialVerificationState) _then;

/// Create a copy of InitialVerificationState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,}) {
  return _then(_LoadInitialVerificationState(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,
  ));
}

/// Create a copy of InitialVerificationState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}

// dart format on
