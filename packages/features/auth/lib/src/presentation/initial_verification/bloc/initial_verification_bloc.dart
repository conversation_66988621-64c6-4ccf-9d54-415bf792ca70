import 'package:auth/src/domain/usecases/get_user_details_usecase.dart';
import 'package:auth/src/navigator/auth_navigator.dart';
import 'package:auth/src/services/domain/usecases/get_current_user_usecase.dart';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'initial_verification_bloc.freezed.dart';
part 'initial_verification_event.dart';
part 'initial_verification_state.dart';

@injectable
class InitialVerificationBloc
    extends Bloc<InitialVerificationEvent, InitialVerificationState> {
  InitialVerificationBloc(
    this._navigator,
    this._getCurrentUserUsecase,
    this._getUserDetailsUsecase,
  ) : super(const InitialVerificationState()) {
    on<InitialVerificationEvent>(
      (event, emit) => switch (event) {
        _Started() => _onLoad(emit, event),
        _ => null,
      },
    );

    add(const _Started());
  }

  final AuthNavigator _navigator;
  final GetCurrentUserUsecase _getCurrentUserUsecase;
  final GetUserDetailsUsecase _getUserDetailsUsecase;
  // final SignOutUsecase _singOutUsecase;

  Future<void> _onLoad(
    Emitter<InitialVerificationState> emit,
    _Started event,
  ) async {
    emit(state.copyWith(status: const ProcessState.loading()));

    // await _singOutUsecase();

    final userId = _getCurrentUserUsecase().getOrNull();

    if (userId.isNotNullOrEmpty) {
      final details = (await _getUserDetailsUsecase(userId!)).getOrNull();
      if (details.isNotNull && details!.isDummyAccount) {
        return _navigator.pushRegisteredChildrenList();
      }
      if (details.isNotNull) {
        _navigator.pushRegisterChildren();
      } else {
        _navigator.pushRegisterUserProfile();
      }
    } else {
      _navigator.pushRegister();
    }
  }
}
