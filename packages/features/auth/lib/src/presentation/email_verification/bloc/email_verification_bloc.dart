import 'package:auth/src/navigator/auth_navigator.dart';
import 'package:auth/src/services/domain/usecases/sign_out_usecase.dart';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

part 'email_verification_bloc.freezed.dart';
part 'email_verification_event.dart';
part 'email_verification_state.dart';

@injectable
class EmailVerificationBloc
    extends Bloc<EmailVerificationEvent, EmailVerificationState> {
  EmailVerificationBloc(
    this._signOutUsecase,
    this._navigator,
  ) : super(const EmailVerificationState()) {
    on<EmailVerificationEvent>(
      (event, emit) => switch (event) {
        _ChangeEmailVerificationEvent() => _onChangeEmail(emit, event),
        _ => null,
      },
    );
  }

  final SignOutUsecase _signOutUsecase;
  final AuthNavigator _navigator;

  Future<void> _onChangeEmail(
    Emitter<EmailVerificationState> emit,
    _ChangeEmailVerificationEvent event,
  ) async {
    await _signOutUsecase();
    _navigator.pushRegister();
  }
}
