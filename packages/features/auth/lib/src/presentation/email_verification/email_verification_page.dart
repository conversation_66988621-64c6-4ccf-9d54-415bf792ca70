import 'package:app_di/app_di.dart';
import 'package:auth/src/presentation/email_verification/bloc/email_verification_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmailVerificationPage extends StatelessWidget {
  const EmailVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return BlocProvider(
      create: (_) => diContainer<EmailVerificationBloc>(),
      child: Scaffold(
        bottomNavigationBar: const BottomButtonsBar(),
        body: SafeArea(
          child: Center(
            child: Padding(
              padding: dimen.x.sm + dimen.y.xs,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircleAvatar(
                    radius: xxxl,
                    child: Icon(
                      Icons.email_outlined,
                      size: xxl,
                    ),
                  ),
                  const SizedBox(height: sm),
                  Text(
                    'Verifica tu correo electrónico',
                    style: theme.appTextTheme.display3,
                  ),
                  const SizedBox(height: sm),
                  Text(
                    '¿No has recibido el código? Revisa tu bandeja de Spam o prueba en unos segundos reenviar el código.',
                    style: theme.appTextTheme.body1,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class BottomButtonsBar extends StatelessWidget {
  const BottomButtonsBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return SafeArea(
      child: Container(
        color: theme.colorScheme.surface,
        padding: dimen.all.xs,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SecondaryButton.responsive(
              title: 'Cambiar el correo electrónico',
              onPressed: () => context.read<EmailVerificationBloc>().add(
                const EmailVerificationEvent.onChangeEmail(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
