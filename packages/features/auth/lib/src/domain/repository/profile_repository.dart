import 'package:auth/src/domain/models/user_details.dart';
import 'package:auth/src/domain/provider/profile_firebase_storage_provider.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class ProfileRepository {
  ProfileRepository(this._provider);

  final ProfileFirebaseStorageProvider _provider;

  Future<Result<void, Exception>> saveUserDetails(
    String userId,
    UserDetails userDetails,
  ) => Result.fromAsync(() => _provider.saveUserDetails(userId, userDetails));

  Future<Result<UserDetails, Exception>> getUserDetails(String userId) =>
      Result.fromAsync(() => _provider.getUserDetails(userId));

  Future<Result<Iterable<UserDetails>, Exception>> getAllUsers() =>
      Result.fromAsync(() => _provider.getAllUsers());
}
