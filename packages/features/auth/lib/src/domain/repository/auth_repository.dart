import 'package:auth/src/domain/models/user.dart';
import 'package:auth/src/domain/provider/auth_firebase_provider.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class AuthRepository {
  AuthRepository(this._provider);

  final AuthFirebaseProvider _provider;

  Future<Result<User, Exception>> signUp({
    required String email,
    required String password,
  }) => Result.fromAsync(
    () => _provider
        .signUp(email, password)
        .then(
          (data) => User(
            email: email,
            userData: data.user,
          ),
        ),
  );

  Future<Result<User, Exception>> signIn({
    required String email,
    required String password,
  }) => Result.fromAsync(
    () => _provider
        .signIn(email, password)
        .then(
          (data) => User(
            email: email,
            userData: data.user,
          ),
        ),
  );

  Future<Result<void, Exception>> signOut() =>
      Result.fromAsync(() => _provider.signOut());

  Result<String, Exception> getCurrentUserID() => Result.fromAction(() {
    final id = _provider.getCurrentUser()?.uid;
    if (id.isNotNullOrEmpty) {
      return id!;
    } else {
      throw Exception('Not logged');
    }
  });
}
