import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'user_details.freezed.dart';
part 'user_details.g.dart';

@freezed
abstract class UserDetails with _$UserDetails {
  factory UserDetails({
    required String name,
    required String lastName,
    required String secondLastName,
    required String identification,
    required String bankAccount,
    required String phone,
    String? emergencyContact,
    @Default(false) bool isAnpa,
    @Default(false) bool isDummyAccount,
    @JsonKey(includeToJson: false) String? id,
  }) = _UserDetails;

  factory UserDetails.fromJson(Map<String, dynamic> json) =>
      _$UserDetailsFromJson(json);

  static List<String> csvHeader() => [
    'Nombre',
    'Apellido',
    'DNI / NIE',
    'Teléfono',
    'Socio ANPA',
    'Cuenta bancaria',
  ];
}

extension UserDetailsX on UserDetails {
  List<String> toCsvRow() => [
    name.toCapitalize(),
    lastName.toCapitalize(),
    identification,
    phone,
    isAnpa.toLocalString(),
    bankAccount.trim(),
  ];
}
