// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserDetails {

 String get name; String get lastName; String get secondLastName; String get identification; String get bankAccount; String get phone; String? get emergencyContact; bool get isAnpa; bool get isDummyAccount;@JsonKey(includeToJson: false) String? get id;
/// Create a copy of UserDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserDetailsCopyWith<UserDetails> get copyWith => _$UserDetailsCopyWithImpl<UserDetails>(this as UserDetails, _$identity);

  /// Serializes this UserDetails to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserDetails&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.secondLastName, secondLastName) || other.secondLastName == secondLastName)&&(identical(other.identification, identification) || other.identification == identification)&&(identical(other.bankAccount, bankAccount) || other.bankAccount == bankAccount)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.emergencyContact, emergencyContact) || other.emergencyContact == emergencyContact)&&(identical(other.isAnpa, isAnpa) || other.isAnpa == isAnpa)&&(identical(other.isDummyAccount, isDummyAccount) || other.isDummyAccount == isDummyAccount)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,lastName,secondLastName,identification,bankAccount,phone,emergencyContact,isAnpa,isDummyAccount,id);

@override
String toString() {
  return 'UserDetails(name: $name, lastName: $lastName, secondLastName: $secondLastName, identification: $identification, bankAccount: $bankAccount, phone: $phone, emergencyContact: $emergencyContact, isAnpa: $isAnpa, isDummyAccount: $isDummyAccount, id: $id)';
}


}

/// @nodoc
abstract mixin class $UserDetailsCopyWith<$Res>  {
  factory $UserDetailsCopyWith(UserDetails value, $Res Function(UserDetails) _then) = _$UserDetailsCopyWithImpl;
@useResult
$Res call({
 String name, String lastName, String secondLastName, String identification, String bankAccount, String phone, String? emergencyContact, bool isAnpa, bool isDummyAccount,@JsonKey(includeToJson: false) String? id
});




}
/// @nodoc
class _$UserDetailsCopyWithImpl<$Res>
    implements $UserDetailsCopyWith<$Res> {
  _$UserDetailsCopyWithImpl(this._self, this._then);

  final UserDetails _self;
  final $Res Function(UserDetails) _then;

/// Create a copy of UserDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? lastName = null,Object? secondLastName = null,Object? identification = null,Object? bankAccount = null,Object? phone = null,Object? emergencyContact = freezed,Object? isAnpa = null,Object? isDummyAccount = null,Object? id = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,secondLastName: null == secondLastName ? _self.secondLastName : secondLastName // ignore: cast_nullable_to_non_nullable
as String,identification: null == identification ? _self.identification : identification // ignore: cast_nullable_to_non_nullable
as String,bankAccount: null == bankAccount ? _self.bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,emergencyContact: freezed == emergencyContact ? _self.emergencyContact : emergencyContact // ignore: cast_nullable_to_non_nullable
as String?,isAnpa: null == isAnpa ? _self.isAnpa : isAnpa // ignore: cast_nullable_to_non_nullable
as bool,isDummyAccount: null == isDummyAccount ? _self.isDummyAccount : isDummyAccount // ignore: cast_nullable_to_non_nullable
as bool,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _UserDetails implements UserDetails {
   _UserDetails({required this.name, required this.lastName, required this.secondLastName, required this.identification, required this.bankAccount, required this.phone, this.emergencyContact, this.isAnpa = false, this.isDummyAccount = false, @JsonKey(includeToJson: false) this.id});
  factory _UserDetails.fromJson(Map<String, dynamic> json) => _$UserDetailsFromJson(json);

@override final  String name;
@override final  String lastName;
@override final  String secondLastName;
@override final  String identification;
@override final  String bankAccount;
@override final  String phone;
@override final  String? emergencyContact;
@override@JsonKey() final  bool isAnpa;
@override@JsonKey() final  bool isDummyAccount;
@override@JsonKey(includeToJson: false) final  String? id;

/// Create a copy of UserDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserDetailsCopyWith<_UserDetails> get copyWith => __$UserDetailsCopyWithImpl<_UserDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserDetailsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserDetails&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.secondLastName, secondLastName) || other.secondLastName == secondLastName)&&(identical(other.identification, identification) || other.identification == identification)&&(identical(other.bankAccount, bankAccount) || other.bankAccount == bankAccount)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.emergencyContact, emergencyContact) || other.emergencyContact == emergencyContact)&&(identical(other.isAnpa, isAnpa) || other.isAnpa == isAnpa)&&(identical(other.isDummyAccount, isDummyAccount) || other.isDummyAccount == isDummyAccount)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,lastName,secondLastName,identification,bankAccount,phone,emergencyContact,isAnpa,isDummyAccount,id);

@override
String toString() {
  return 'UserDetails(name: $name, lastName: $lastName, secondLastName: $secondLastName, identification: $identification, bankAccount: $bankAccount, phone: $phone, emergencyContact: $emergencyContact, isAnpa: $isAnpa, isDummyAccount: $isDummyAccount, id: $id)';
}


}

/// @nodoc
abstract mixin class _$UserDetailsCopyWith<$Res> implements $UserDetailsCopyWith<$Res> {
  factory _$UserDetailsCopyWith(_UserDetails value, $Res Function(_UserDetails) _then) = __$UserDetailsCopyWithImpl;
@override @useResult
$Res call({
 String name, String lastName, String secondLastName, String identification, String bankAccount, String phone, String? emergencyContact, bool isAnpa, bool isDummyAccount,@JsonKey(includeToJson: false) String? id
});




}
/// @nodoc
class __$UserDetailsCopyWithImpl<$Res>
    implements _$UserDetailsCopyWith<$Res> {
  __$UserDetailsCopyWithImpl(this._self, this._then);

  final _UserDetails _self;
  final $Res Function(_UserDetails) _then;

/// Create a copy of UserDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? lastName = null,Object? secondLastName = null,Object? identification = null,Object? bankAccount = null,Object? phone = null,Object? emergencyContact = freezed,Object? isAnpa = null,Object? isDummyAccount = null,Object? id = freezed,}) {
  return _then(_UserDetails(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,secondLastName: null == secondLastName ? _self.secondLastName : secondLastName // ignore: cast_nullable_to_non_nullable
as String,identification: null == identification ? _self.identification : identification // ignore: cast_nullable_to_non_nullable
as String,bankAccount: null == bankAccount ? _self.bankAccount : bankAccount // ignore: cast_nullable_to_non_nullable
as String,phone: null == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String,emergencyContact: freezed == emergencyContact ? _self.emergencyContact : emergencyContact // ignore: cast_nullable_to_non_nullable
as String?,isAnpa: null == isAnpa ? _self.isAnpa : isAnpa // ignore: cast_nullable_to_non_nullable
as bool,isDummyAccount: null == isDummyAccount ? _self.isDummyAccount : isDummyAccount // ignore: cast_nullable_to_non_nullable
as bool,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
