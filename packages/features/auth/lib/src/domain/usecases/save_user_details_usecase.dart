import 'package:auth/src/domain/models/user_details.dart';
import 'package:auth/src/domain/repository/profile_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class SaveUserDetailsUsecase {
  SaveUserDetailsUsecase(this._repository);

  final ProfileRepository _repository;

  Future<Result<void, Exception>> call(
    String userId,
    UserDetails userDetails,
  ) => _repository.saveUserDetails(userId, userDetails);
}
