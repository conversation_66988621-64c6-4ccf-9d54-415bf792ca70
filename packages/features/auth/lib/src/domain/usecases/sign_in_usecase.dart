import 'package:auth/src/domain/models/user.dart';
import 'package:auth/src/domain/repository/auth_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class SignInUsecase {
  SignInUsecase(this._repository);

  final AuthRepository _repository;

  Future<Result<User, Exception>> call({
    required String email,
    required String password,
  }) => _repository.signIn(email: email, password: password);
}
