import 'package:auth/src/domain/models/user_details.dart';
import 'package:auth/src/domain/repository/profile_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class GetUserDetailsUsecase {
  GetUserDetailsUsecase(this._repository);

  final ProfileRepository _repository;

  Future<Result<UserDetails, Exception>> call(String userId) =>
      _repository.getUserDetails(userId);
}
