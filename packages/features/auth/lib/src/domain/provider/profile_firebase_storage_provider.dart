import 'package:auth/src/domain/models/user_details.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';

@injectable
class ProfileFirebaseStorageProvider {
  ProfileFirebaseStorageProvider(this._store);

  final FirebaseFirestore _store;

  CollectionReference<UserDetails> get _usersDocRef => _store
      .collection('users')
      .withConverter<UserDetails>(
        fromFirestore: (json, _) =>
            UserDetails.fromJson({'id': json.id, ...json.data()!}),
        toFirestore: (UserDetails details, _) => details.toJson(),
      );
  Future<void> saveUserDetails(
    String userId,
    UserDetails userDetails,
  ) => _usersDocRef.doc(userId).set(userDetails);

  Future<UserDetails> getUserDetails(String userId) =>
      _usersDocRef.doc(userId).get().then((data) => data.data()!);

  Future<Iterable<UserDetails>> getAllUsers() => _usersDocRef.get().then(
    (data) => data.docs.map(
      (el) => el.data(),
    ),
  );
}
