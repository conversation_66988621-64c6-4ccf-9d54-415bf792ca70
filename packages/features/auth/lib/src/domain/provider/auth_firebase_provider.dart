import 'package:firebase_auth/firebase_auth.dart';
import 'package:injectable/injectable.dart';

@injectable
class AuthFirebaseProvider {
  AuthFirebaseProvider(this._auth);

  final FirebaseAuth _auth;

  Future<UserCredential> signUp(
    String email,
    String password,
  ) async => await _auth.createUserWithEmailAndPassword(
    email: email.trim(),
    password: password.trim(),
  );

  Future<UserCredential> signIn(
    String email,
    String password,
  ) async => await _auth.signInWithEmailAndPassword(
    email: email.trim(),
    password: password.trim(),
  );

  Future<void> signOut() async => await _auth.signOut();

  User? getCurrentUser() => _auth.currentUser;
}
