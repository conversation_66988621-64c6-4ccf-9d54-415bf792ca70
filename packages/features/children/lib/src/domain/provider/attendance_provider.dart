import 'dart:convert';

import 'package:children/src/domain/models/attendance.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';
import 'package:shared_preferences/shared_preferences.dart';

@injectable
class AttendanceProvider {
  AttendanceProvider(this._store, this._prefs);

  final FirebaseFirestore _store;
  final SharedPreferences _prefs;

  String get _docId => DateTime.now().toMonthYear();

  String get _todayCollectionId => DateTime.now().toMonthDay();

  CollectionReference<Attendance> _docRef() {
    return _store
        .collection('attendance')
        .doc(_docId)
        .collection(_todayCollectionId)
        .withConverter<Attendance>(
          fromFirestore: (json, _) => Attendance.fromJson({
            'id': json.id,
            ...json.data()!,
          }),
          toFirestore: (Attendance attendance, _) => attendance.toJson(),
        );
  }

  Future<void> setAttendance(
    Attendance attendance,
  ) => _docRef().doc(attendance.childId).set(attendance);

  Future<Map<String, bool?>> getTodayAttendance() async {
    final prefs = await _prefs;
    final attendance = prefs.getString(_todayCollectionId) ?? '';
    if (attendance.isEmpty) return {};
    final decodedAttendance = jsonDecode(attendance) as Map<String, dynamic>;
    final todayAttendance = decodedAttendance.map(
      (key, value) => MapEntry(key, value as bool?),
    );
    return todayAttendance;
  }

  Future<void> saveTodayAttendance(Map<String, bool?> newAttendance) async {
    final prefs = await _prefs;
    final attendance = prefs.getString(_todayCollectionId) ?? '';
    if (attendance.isEmpty) {
      final encoded = jsonEncode(newAttendance);
      await prefs.setString(_todayCollectionId, encoded);
    } else {
      await prefs.setString(_todayCollectionId, '');
      final Map<String, dynamic> decodedAttendance =
          jsonDecode(attendance) as Map<String, dynamic>;
      final Map<String, bool?> todayAttendance = decodedAttendance.map(
        (key, value) => MapEntry(key, value as bool?),
      );
      todayAttendance.addAll(newAttendance);

      final encoded = jsonEncode(todayAttendance);
      await prefs.setString(_todayCollectionId, encoded);
    }
  }
}
