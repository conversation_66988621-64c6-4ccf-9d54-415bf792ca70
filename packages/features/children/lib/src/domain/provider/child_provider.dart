import 'package:children/src/domain/models/child.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';

@injectable
class ChildProvider {
  ChildProvider(this._store);

  final FirebaseFirestore _store;

  CollectionReference<Child> _childDocRef(String userId) => _store
      .collection('users')
      .doc(userId)
      .collection('children')
      .withConverter<Child>(
        fromFirestore: (json, _) {
          return Child.fromJson({'id': json.id, ...json.data()!});
        },
        toFirestore: (Child child, _) => child.toJson(),
      );

  Future<void> saveChild(
    String userId,
    Child child,
  ) => _childDocRef(userId).add(child);

  Future<List<Child>> getChildrenFromParent(String userId) async {
    final snapshot = await _childDocRef(userId).get();
    return snapshot.docs
        .map((element) => element.data().copyWith(id: element.id))
        .toList();
  }

  Future<Child> getChildDetails(String userId, String childId) async =>
      _childDocRef(userId).doc(childId).get().then((value) => value.data()!);

  Future<void> updateChildDetails(
    String userId,
    String childId,
    Child child,
  ) async => _childDocRef(userId).doc(childId).update(child.toJson());
}
