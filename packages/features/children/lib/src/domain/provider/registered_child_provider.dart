import 'package:children/src/domain/models/registered_child.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';

@injectable
class RegisteredChildProvider {
  RegisteredChildProvider(this._store);

  final FirebaseFirestore _store;

  CollectionReference<RegisteredChild> _childDocRef() => _store
      .collection('registeredChildren')
      .withConverter<RegisteredChild>(
        fromFirestore: (json, _) => RegisteredChild.fromJson({
          'id': json.id,
          ...json.data()!,
        }),
        toFirestore: (RegisteredChild child, _) => child.toJson(),
      );

  Future<void> saveChild(
    String userId,
    RegisteredChild child,
  ) => _childDocRef().add(child);

  Future<List<RegisteredChild>> getChildren() async {
    final snapshot = await _childDocRef().get();
    return snapshot.docs.map((element) => element.data()).toList();
  }

  Future<RegisteredChild> getChildDetails(String childId) async =>
      _childDocRef().doc(childId).get().then((value) => value.data()!);

  Future<void> updateChildDetails(
    String userId,
    String childId,
    RegisteredChild child,
  ) async => _childDocRef().doc(childId).update(child.toJson());

  Future<void> saveBulkChildren(List<RegisteredChild> children) async {
    final batch = _store.batch();
    for (final child in children) {
      batch.set(_childDocRef().doc(), child);
    }
    await batch.commit();
  }
}
