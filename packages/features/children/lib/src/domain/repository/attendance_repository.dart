import 'package:children/src/domain/models/attendance.dart';
import 'package:children/src/domain/provider/attendance_provider.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class AttendanceRepository {
  AttendanceRepository(this._provider);

  final AttendanceProvider _provider;

  Future<Result<void, Exception>> setAttendance({
    required String parentId,
    required String childId,
    bool attendance = true,
  }) => Result.fromAsync(
    () => _provider.setAttendance(
      Attendance(
        childId: childId,
        parentId: parentId,
        date: DateTime.now(),
        attendance: attendance,
      ),
    ),
  );

  Future<Result<Map<String, bool?>, Exception>> getTodayAttendance() =>
      Result.fromAsync(_provider.getTodayAttendance);

  Future<Result<void, Exception>> saveTodayAttendance(
    Map<String, bool?> assistance,
  ) => Result.fromAsync(() => _provider.saveTodayAttendance(assistance));
}
