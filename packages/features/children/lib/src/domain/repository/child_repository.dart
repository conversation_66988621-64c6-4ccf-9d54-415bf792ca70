import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/provider/child_provider.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class ChildRepository {
  ChildRepository(this._provider);

  final ChildProvider _provider;

  Future<Result<void, Exception>> saveChild(
    String userId,
    Child child,
  ) => Result.fromAsync(() => _provider.saveChild(userId, child));

  Future<Result<List<Child>, Exception>> getChildrenFromParent(String userId) =>
      Result.fromAsync(
        () => _provider.getChildrenFromParent(userId),
      );

  Future<Result<Child, Exception>> getChildDetails(
    String userId,
    String childId,
  ) => Result.fromAsync(
    () => _provider.getChildDetails(userId, childId),
  );

  Future<Result<void, Exception>> updateChild(
    String userId,
    String childId,
    Child child,
  ) => Result.fromAsync(
    () => _provider.updateChildDetails(userId, childId, child),
  );
}
