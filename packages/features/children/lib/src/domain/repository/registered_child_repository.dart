import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/domain/provider/registered_child_provider.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class RegisteredChildRepository {
  RegisteredChildRepository(this._provider);

  final RegisteredChildProvider _provider;

  Future<Result<void, Exception>> saveBulkChildren(
    List<RegisteredChild> children,
  ) async => Result.fromAsync(
    () => _provider.saveBulkChildren(children),
  );

  Future<Result<List<RegisteredChild>, Exception>> getChildren() =>
      Result.fromAsync(_provider.getChildren);
}
