import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/domain/repository/attendance_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class SaveAttendanceForChildUsecase {
  SaveAttendanceForChildUsecase(this._repository);

  final AttendanceRepository _repository;

  Future<Result<void, Exception>> call({
    required RegisteredChild child,
    bool attendance = true,
  }) => _repository.setAttendance(
    childId: child.childId,
    parentId: child.parentId,
    attendance: attendance,
  );
}
