import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/repository/child_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class GetChildrenUsecase {
  GetChildrenUsecase(this._repository);

  final ChildRepository _repository;

  Future<Result<List<Child>, Exception>> call(String userId) =>
      _repository.getChildrenFromParent(userId);
}
