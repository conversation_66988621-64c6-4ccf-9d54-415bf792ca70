import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/repository/child_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class SaveChildUsecase {
  SaveChildUsecase(this._repository);

  final ChildRepository _repository;

  Future<Result<void, Exception>> call(String userId, Child child) =>
      _repository.saveChild(userId, child);
}
