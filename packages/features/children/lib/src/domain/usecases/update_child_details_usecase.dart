import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/repository/child_repository.dart';
import 'package:injectable/injectable.dart';

@injectable
class UpdateChildDetailsUsecase {
  UpdateChildDetailsUsecase(this._repository);

  final ChildRepository _repository;

  Future<void> call(String userId, String childId, Child child) async {
    await _repository.updateChild(userId, childId, child);
    await _repository.getChildrenFromParent(userId);
  }
}
