import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/domain/repository/registered_child_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class GetRegisteredChildrenUsecase {
  GetRegisteredChildrenUsecase(this._repository);

  final RegisteredChildRepository _repository;

  Future<Result<List<RegisteredChild>, Exception>> call() =>
      _repository.getChildren();
}
