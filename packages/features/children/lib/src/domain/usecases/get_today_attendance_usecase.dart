import 'package:children/src/domain/repository/attendance_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class GetTodayAttendanceUsecase {
  GetTodayAttendanceUsecase(this._attendanceRepository);
  final AttendanceRepository _attendanceRepository;

  Future<Result<Map<String, bool?>, Exception>> call() async {
    return _attendanceRepository.getTodayAttendance();
  }
}
