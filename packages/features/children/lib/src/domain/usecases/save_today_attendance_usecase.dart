import 'package:children/src/domain/repository/attendance_repository.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

@injectable
class SaveTodayAttendanceUsecase {
  SaveTodayAttendanceUsecase(this._attendanceRepository);

  final AttendanceRepository _attendanceRepository;

  Future<Result<void, Exception>> call(Map<String, bool?> assistance) =>
      _attendanceRepository.saveTodayAttendance(assistance);
}
