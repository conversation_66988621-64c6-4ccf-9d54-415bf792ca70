// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'child.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Child _$ChildFromJson(Map<String, dynamic> json) => _Child(
  name: json['name'] as String,
  lastName: json['lastName'] as String,
  dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
  schoolYear: $enumDecode(
    _$SchoolYearEnumMap,
    json['schoolYear'],
    unknownValue: SchoolYear.unknown,
  ),
  alergies: (json['alergies'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  weekAssitance: (json['weekAssitance'] as List<dynamic>)
      .map((e) => $enumDecode(_$WeekDaysEnumMap, e))
      .toSet(),
  avatarUrl: json['avatarUrl'] as String,
  hasAttendendInPreviousCourses: json['hasAttendendInPreviousCourses'] as bool,
  hasSiblingsWhoNeedsDinning: json['hasSiblingsWhoNeedsDinning'] as bool,
  hasDinningScholarship: json['hasDinningScholarship'] as bool,
  isDinningIn: json['isDinningIn'] as bool,
  startingDate: $enumDecodeNullable(
    _$StartingDateEnumMap,
    json['startingDate'],
    unknownValue: StartingDate.unknown,
  ),
  isAllowedToExit: json['isAllowedToExit'] as bool? ?? false,
  id: json['id'] as String?,
);

Map<String, dynamic> _$ChildToJson(_Child instance) => <String, dynamic>{
  'name': instance.name,
  'lastName': instance.lastName,
  'dateOfBirth': instance.dateOfBirth.toIso8601String(),
  'schoolYear': _$SchoolYearEnumMap[instance.schoolYear]!,
  'alergies': instance.alergies,
  'weekAssitance': instance.weekAssitance
      .map((e) => _$WeekDaysEnumMap[e]!)
      .toList(),
  'avatarUrl': instance.avatarUrl,
  'hasAttendendInPreviousCourses': instance.hasAttendendInPreviousCourses,
  'hasSiblingsWhoNeedsDinning': instance.hasSiblingsWhoNeedsDinning,
  'hasDinningScholarship': instance.hasDinningScholarship,
  'isDinningIn': instance.isDinningIn,
  'startingDate': _$StartingDateEnumMap[instance.startingDate],
  if (instance.isAllowedToExit case final value?) 'isAllowedToExit': value,
};

const _$SchoolYearEnumMap = {
  SchoolYear.eso1: 'ESO 1',
  SchoolYear.eso2: 'ESO 2',
  SchoolYear.eso3: 'ESO 3',
  SchoolYear.eso4: 'ESO 4',
  SchoolYear.bachiller1: 'BACHILLER 1',
  SchoolYear.bachiller2: 'BACHILLER 2',
  SchoolYear.fp: 'FP ADULTOS',
  SchoolYear.unknown: 'unknown',
};

const _$WeekDaysEnumMap = {
  WeekDays.monday: 'MONDAY',
  WeekDays.tuesday: 'TUESDAY',
  WeekDays.wednesday: 'WEDNESDAY',
  WeekDays.thursday: 'THURSDAY',
  WeekDays.friday: 'FRIDAY',
  WeekDays.unknown: 'unknown',
};

const _$StartingDateEnumMap = {
  StartingDate.september2024: 'Septiembre 2024',
  StartingDate.obtober2024: 'Octubre 2024',
  StartingDate.unknown: 'unknown',
};
