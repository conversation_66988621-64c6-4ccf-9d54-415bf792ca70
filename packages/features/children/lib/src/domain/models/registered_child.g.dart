// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'registered_child.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_RegisteredChild _$RegisteredChildFromJson(Map<String, dynamic> json) =>
    _RegisteredChild(
      name: json['name'] as String,
      lastName: json['lastName'] as String,
      dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
      schoolYear: $enumDecode(
        _$SchoolYearEnumMap,
        json['schoolYear'],
        unknownValue: SchoolYear.unknown,
      ),
      alergies: (json['alergies'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      weekAssitance: (json['weekAssitance'] as List<dynamic>)
          .map((e) => $enumDecode(_$WeekDaysEnumMap, e))
          .toSet(),
      hasDinningScholarship: json['hasDinningScholarship'] as bool,
      isDinningIn: json['isDinningIn'] as bool,
      childId: json['childId'] as String,
      parentId: json['parentId'] as String,
      isAllowedToExit: json['isAllowedToExit'] as bool? ?? false,
    );

Map<String, dynamic> _$RegisteredChildToJson(_RegisteredChild instance) =>
    <String, dynamic>{
      'name': instance.name,
      'lastName': instance.lastName,
      'dateOfBirth': instance.dateOfBirth.toIso8601String(),
      'schoolYear': _$SchoolYearEnumMap[instance.schoolYear]!,
      'alergies': instance.alergies,
      'weekAssitance': instance.weekAssitance
          .map((e) => _$WeekDaysEnumMap[e]!)
          .toList(),
      'hasDinningScholarship': instance.hasDinningScholarship,
      'isDinningIn': instance.isDinningIn,
      'childId': instance.childId,
      'parentId': instance.parentId,
      if (instance.isAllowedToExit case final value?) 'isAllowedToExit': value,
    };

const _$SchoolYearEnumMap = {
  SchoolYear.eso1: 'ESO 1',
  SchoolYear.eso2: 'ESO 2',
  SchoolYear.eso3: 'ESO 3',
  SchoolYear.eso4: 'ESO 4',
  SchoolYear.bachiller1: 'BACHILLER 1',
  SchoolYear.bachiller2: 'BACHILLER 2',
  SchoolYear.fp: 'FP ADULTOS',
  SchoolYear.unknown: 'unknown',
};

const _$WeekDaysEnumMap = {
  WeekDays.monday: 'MONDAY',
  WeekDays.tuesday: 'TUESDAY',
  WeekDays.wednesday: 'WEDNESDAY',
  WeekDays.thursday: 'THURSDAY',
  WeekDays.friday: 'FRIDAY',
  WeekDays.unknown: 'unknown',
};
