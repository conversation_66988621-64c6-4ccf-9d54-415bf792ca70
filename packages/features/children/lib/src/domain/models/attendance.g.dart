// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Attendance _$AttendanceFromJson(Map<String, dynamic> json) => _Attendance(
  childId: json['childId'] as String,
  parentId: json['parentId'] as String,
  date: const CustomDateTimeConverter().fromJson(json['date'] as String),
  attendance: json['attendance'] as bool,
);

Map<String, dynamic> _$AttendanceToJson(_Attendance instance) =>
    <String, dynamic>{
      'childId': instance.childId,
      'parentId': instance.parentId,
      'date': const CustomDateTimeConverter().toJson(instance.date),
      'attendance': instance.attendance,
    };
