import 'package:freezed_annotation/freezed_annotation.dart';

@JsonEnum()
enum SchoolYear {
  @JsonValue('ESO 1')
  eso1,
  @JsonValue('ESO 2')
  eso2,
  @<PERSON>sonValue('ESO 3')
  eso3,
  @<PERSON><PERSON>Value('ESO 4')
  eso4,
  @<PERSON>sonValue('BACHILLER 1')
  bachiller1,
  @<PERSON>sonValue('BACHILLER 2')
  bachiller2,
  @JsonValue('FP ADULTOS')
  fp,
  unknown,
}

extension SchoolYearX on SchoolYear {
  String getName([bool toTile = false]) => switch (this) {
    SchoolYear.eso1 => '1° ESO',
    SchoolYear.eso2 => '2° ESO',
    SchoolYear.eso3 => '3° ESO',
    SchoolYear.eso4 => '4° ESO',
    SchoolYear.bachiller1 => '1° BACHILLER',
    SchoolYear.bachiller2 => '2° BACHILLER',
    SchoolYear.fp => 'FP ADULTOS',
    _ => toTile ? '' : 'NONE',
  };
}
