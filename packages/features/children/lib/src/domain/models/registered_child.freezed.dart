// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'registered_child.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RegisteredChild {

 String get name; String get lastName; DateTime get dateOfBirth;@JsonKey(unknownEnumValue: SchoolYear.unknown) SchoolYear get schoolYear; List<String> get alergies; Set<WeekDays> get weekAssitance; bool get hasDinningScholarship; bool get isDinningIn; String get childId; String get parentId; bool? get isAllowedToExit;
/// Create a copy of RegisteredChild
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RegisteredChildCopyWith<RegisteredChild> get copyWith => _$RegisteredChildCopyWithImpl<RegisteredChild>(this as RegisteredChild, _$identity);

  /// Serializes this RegisteredChild to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisteredChild&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.schoolYear, schoolYear) || other.schoolYear == schoolYear)&&const DeepCollectionEquality().equals(other.alergies, alergies)&&const DeepCollectionEquality().equals(other.weekAssitance, weekAssitance)&&(identical(other.hasDinningScholarship, hasDinningScholarship) || other.hasDinningScholarship == hasDinningScholarship)&&(identical(other.isDinningIn, isDinningIn) || other.isDinningIn == isDinningIn)&&(identical(other.childId, childId) || other.childId == childId)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.isAllowedToExit, isAllowedToExit) || other.isAllowedToExit == isAllowedToExit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,lastName,dateOfBirth,schoolYear,const DeepCollectionEquality().hash(alergies),const DeepCollectionEquality().hash(weekAssitance),hasDinningScholarship,isDinningIn,childId,parentId,isAllowedToExit);

@override
String toString() {
  return 'RegisteredChild(name: $name, lastName: $lastName, dateOfBirth: $dateOfBirth, schoolYear: $schoolYear, alergies: $alergies, weekAssitance: $weekAssitance, hasDinningScholarship: $hasDinningScholarship, isDinningIn: $isDinningIn, childId: $childId, parentId: $parentId, isAllowedToExit: $isAllowedToExit)';
}


}

/// @nodoc
abstract mixin class $RegisteredChildCopyWith<$Res>  {
  factory $RegisteredChildCopyWith(RegisteredChild value, $Res Function(RegisteredChild) _then) = _$RegisteredChildCopyWithImpl;
@useResult
$Res call({
 String name, String lastName, DateTime dateOfBirth,@JsonKey(unknownEnumValue: SchoolYear.unknown) SchoolYear schoolYear, List<String> alergies, Set<WeekDays> weekAssitance, bool hasDinningScholarship, bool isDinningIn, String childId, String parentId, bool? isAllowedToExit
});




}
/// @nodoc
class _$RegisteredChildCopyWithImpl<$Res>
    implements $RegisteredChildCopyWith<$Res> {
  _$RegisteredChildCopyWithImpl(this._self, this._then);

  final RegisteredChild _self;
  final $Res Function(RegisteredChild) _then;

/// Create a copy of RegisteredChild
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? lastName = null,Object? dateOfBirth = null,Object? schoolYear = null,Object? alergies = null,Object? weekAssitance = null,Object? hasDinningScholarship = null,Object? isDinningIn = null,Object? childId = null,Object? parentId = null,Object? isAllowedToExit = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,dateOfBirth: null == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime,schoolYear: null == schoolYear ? _self.schoolYear : schoolYear // ignore: cast_nullable_to_non_nullable
as SchoolYear,alergies: null == alergies ? _self.alergies : alergies // ignore: cast_nullable_to_non_nullable
as List<String>,weekAssitance: null == weekAssitance ? _self.weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Set<WeekDays>,hasDinningScholarship: null == hasDinningScholarship ? _self.hasDinningScholarship : hasDinningScholarship // ignore: cast_nullable_to_non_nullable
as bool,isDinningIn: null == isDinningIn ? _self.isDinningIn : isDinningIn // ignore: cast_nullable_to_non_nullable
as bool,childId: null == childId ? _self.childId : childId // ignore: cast_nullable_to_non_nullable
as String,parentId: null == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String,isAllowedToExit: freezed == isAllowedToExit ? _self.isAllowedToExit : isAllowedToExit // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _RegisteredChild implements RegisteredChild {
   _RegisteredChild({required this.name, required this.lastName, required this.dateOfBirth, @JsonKey(unknownEnumValue: SchoolYear.unknown) required this.schoolYear, required final  List<String> alergies, required final  Set<WeekDays> weekAssitance, required this.hasDinningScholarship, required this.isDinningIn, required this.childId, required this.parentId, this.isAllowedToExit = false}): _alergies = alergies,_weekAssitance = weekAssitance;
  factory _RegisteredChild.fromJson(Map<String, dynamic> json) => _$RegisteredChildFromJson(json);

@override final  String name;
@override final  String lastName;
@override final  DateTime dateOfBirth;
@override@JsonKey(unknownEnumValue: SchoolYear.unknown) final  SchoolYear schoolYear;
 final  List<String> _alergies;
@override List<String> get alergies {
  if (_alergies is EqualUnmodifiableListView) return _alergies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_alergies);
}

 final  Set<WeekDays> _weekAssitance;
@override Set<WeekDays> get weekAssitance {
  if (_weekAssitance is EqualUnmodifiableSetView) return _weekAssitance;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableSetView(_weekAssitance);
}

@override final  bool hasDinningScholarship;
@override final  bool isDinningIn;
@override final  String childId;
@override final  String parentId;
@override@JsonKey() final  bool? isAllowedToExit;

/// Create a copy of RegisteredChild
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RegisteredChildCopyWith<_RegisteredChild> get copyWith => __$RegisteredChildCopyWithImpl<_RegisteredChild>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RegisteredChildToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RegisteredChild&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.schoolYear, schoolYear) || other.schoolYear == schoolYear)&&const DeepCollectionEquality().equals(other._alergies, _alergies)&&const DeepCollectionEquality().equals(other._weekAssitance, _weekAssitance)&&(identical(other.hasDinningScholarship, hasDinningScholarship) || other.hasDinningScholarship == hasDinningScholarship)&&(identical(other.isDinningIn, isDinningIn) || other.isDinningIn == isDinningIn)&&(identical(other.childId, childId) || other.childId == childId)&&(identical(other.parentId, parentId) || other.parentId == parentId)&&(identical(other.isAllowedToExit, isAllowedToExit) || other.isAllowedToExit == isAllowedToExit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,lastName,dateOfBirth,schoolYear,const DeepCollectionEquality().hash(_alergies),const DeepCollectionEquality().hash(_weekAssitance),hasDinningScholarship,isDinningIn,childId,parentId,isAllowedToExit);

@override
String toString() {
  return 'RegisteredChild(name: $name, lastName: $lastName, dateOfBirth: $dateOfBirth, schoolYear: $schoolYear, alergies: $alergies, weekAssitance: $weekAssitance, hasDinningScholarship: $hasDinningScholarship, isDinningIn: $isDinningIn, childId: $childId, parentId: $parentId, isAllowedToExit: $isAllowedToExit)';
}


}

/// @nodoc
abstract mixin class _$RegisteredChildCopyWith<$Res> implements $RegisteredChildCopyWith<$Res> {
  factory _$RegisteredChildCopyWith(_RegisteredChild value, $Res Function(_RegisteredChild) _then) = __$RegisteredChildCopyWithImpl;
@override @useResult
$Res call({
 String name, String lastName, DateTime dateOfBirth,@JsonKey(unknownEnumValue: SchoolYear.unknown) SchoolYear schoolYear, List<String> alergies, Set<WeekDays> weekAssitance, bool hasDinningScholarship, bool isDinningIn, String childId, String parentId, bool? isAllowedToExit
});




}
/// @nodoc
class __$RegisteredChildCopyWithImpl<$Res>
    implements _$RegisteredChildCopyWith<$Res> {
  __$RegisteredChildCopyWithImpl(this._self, this._then);

  final _RegisteredChild _self;
  final $Res Function(_RegisteredChild) _then;

/// Create a copy of RegisteredChild
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? lastName = null,Object? dateOfBirth = null,Object? schoolYear = null,Object? alergies = null,Object? weekAssitance = null,Object? hasDinningScholarship = null,Object? isDinningIn = null,Object? childId = null,Object? parentId = null,Object? isAllowedToExit = freezed,}) {
  return _then(_RegisteredChild(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,dateOfBirth: null == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime,schoolYear: null == schoolYear ? _self.schoolYear : schoolYear // ignore: cast_nullable_to_non_nullable
as SchoolYear,alergies: null == alergies ? _self._alergies : alergies // ignore: cast_nullable_to_non_nullable
as List<String>,weekAssitance: null == weekAssitance ? _self._weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Set<WeekDays>,hasDinningScholarship: null == hasDinningScholarship ? _self.hasDinningScholarship : hasDinningScholarship // ignore: cast_nullable_to_non_nullable
as bool,isDinningIn: null == isDinningIn ? _self.isDinningIn : isDinningIn // ignore: cast_nullable_to_non_nullable
as bool,childId: null == childId ? _self.childId : childId // ignore: cast_nullable_to_non_nullable
as String,parentId: null == parentId ? _self.parentId : parentId // ignore: cast_nullable_to_non_nullable
as String,isAllowedToExit: freezed == isAllowedToExit ? _self.isAllowedToExit : isAllowedToExit // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

// dart format on
