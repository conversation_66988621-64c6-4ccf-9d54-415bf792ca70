import 'package:children/src/domain/models/school_year.dart';
import 'package:children/src/domain/models/starting_date.dart';
import 'package:children/src/domain/models/week_days.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:prelude/prelude.dart';

part 'child.freezed.dart';
part 'child.g.dart';

@freezed
abstract class Child with _$Child {
  factory Child({
    required String name,
    required String lastName,
    required DateTime dateOfBirth,
    @<PERSON><PERSON><PERSON><PERSON>(unknownEnumValue: SchoolYear.unknown)
    required SchoolYear schoolYear,
    required List<String> alergies,
    required Set<WeekDays> weekAssitance,
    required String avatarUrl,
    required bool hasAttendendInPreviousCourses,
    required bool hasSiblingsWhoNeedsDinning,
    required bool hasDinningScholarship,
    required bool isDinningIn,
    @JsonKey(unknownEnumValue: StartingDate.unknown, includeIfNull: true)
    required StartingDate? startingDate,
    @Default(false) bool? isAllowedToExit,
    @JsonKey(includeToJson: false) String? id,
  }) = _Child;

  factory Child.fromJson(Map<String, dynamic> json) => _$Child<PERSON>son(json);

  static List<String> csvHeader() => [
    'Número',
    'Nombre',
    'Apellidos',
    'Curso',
    'Fecha de inicio',
    'Asistencia',
    'Comerá en el centro',
    'Beca comedor',
    'Alergias',
    'Salida',
  ];
}

extension ChildX on Child {
  List<String> toCsvRow() => [
    name.toCapitalize(),
    lastName.toCapitalize(),
    schoolYear.getName(),
    startingDate?.getName() ?? '',
    weekAssitance
        .map(
          (e) => e.toLocalString(),
        )
        .join('\n'),
    isDinningIn.toLocalString(),
    hasDinningScholarship.toLocalString(),
    alergies.join(', '),
  ];
}
