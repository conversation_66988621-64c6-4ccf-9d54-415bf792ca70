// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'prices.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Prices {

 List<int> get shortMonthPrices; List<int> get longMonthPrices;
/// Create a copy of Prices
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PricesCopyWith<Prices> get copyWith => _$PricesCopyWithImpl<Prices>(this as Prices, _$identity);

  /// Serializes this Prices to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Prices&&const DeepCollectionEquality().equals(other.shortMonthPrices, shortMonthPrices)&&const DeepCollectionEquality().equals(other.longMonthPrices, longMonthPrices));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(shortMonthPrices),const DeepCollectionEquality().hash(longMonthPrices));

@override
String toString() {
  return 'Prices(shortMonthPrices: $shortMonthPrices, longMonthPrices: $longMonthPrices)';
}


}

/// @nodoc
abstract mixin class $PricesCopyWith<$Res>  {
  factory $PricesCopyWith(Prices value, $Res Function(Prices) _then) = _$PricesCopyWithImpl;
@useResult
$Res call({
 List<int> shortMonthPrices, List<int> longMonthPrices
});




}
/// @nodoc
class _$PricesCopyWithImpl<$Res>
    implements $PricesCopyWith<$Res> {
  _$PricesCopyWithImpl(this._self, this._then);

  final Prices _self;
  final $Res Function(Prices) _then;

/// Create a copy of Prices
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? shortMonthPrices = null,Object? longMonthPrices = null,}) {
  return _then(_self.copyWith(
shortMonthPrices: null == shortMonthPrices ? _self.shortMonthPrices : shortMonthPrices // ignore: cast_nullable_to_non_nullable
as List<int>,longMonthPrices: null == longMonthPrices ? _self.longMonthPrices : longMonthPrices // ignore: cast_nullable_to_non_nullable
as List<int>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Prices implements Prices {
   _Prices({final  List<int> shortMonthPrices = const [20, 30, 45, 60, 70], final  List<int> longMonthPrices = const [25, 45, 65, 90, 110]}): _shortMonthPrices = shortMonthPrices,_longMonthPrices = longMonthPrices;
  factory _Prices.fromJson(Map<String, dynamic> json) => _$PricesFromJson(json);

 final  List<int> _shortMonthPrices;
@override@JsonKey() List<int> get shortMonthPrices {
  if (_shortMonthPrices is EqualUnmodifiableListView) return _shortMonthPrices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_shortMonthPrices);
}

 final  List<int> _longMonthPrices;
@override@JsonKey() List<int> get longMonthPrices {
  if (_longMonthPrices is EqualUnmodifiableListView) return _longMonthPrices;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_longMonthPrices);
}


/// Create a copy of Prices
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PricesCopyWith<_Prices> get copyWith => __$PricesCopyWithImpl<_Prices>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PricesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Prices&&const DeepCollectionEquality().equals(other._shortMonthPrices, _shortMonthPrices)&&const DeepCollectionEquality().equals(other._longMonthPrices, _longMonthPrices));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_shortMonthPrices),const DeepCollectionEquality().hash(_longMonthPrices));

@override
String toString() {
  return 'Prices(shortMonthPrices: $shortMonthPrices, longMonthPrices: $longMonthPrices)';
}


}

/// @nodoc
abstract mixin class _$PricesCopyWith<$Res> implements $PricesCopyWith<$Res> {
  factory _$PricesCopyWith(_Prices value, $Res Function(_Prices) _then) = __$PricesCopyWithImpl;
@override @useResult
$Res call({
 List<int> shortMonthPrices, List<int> longMonthPrices
});




}
/// @nodoc
class __$PricesCopyWithImpl<$Res>
    implements _$PricesCopyWith<$Res> {
  __$PricesCopyWithImpl(this._self, this._then);

  final _Prices _self;
  final $Res Function(_Prices) _then;

/// Create a copy of Prices
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? shortMonthPrices = null,Object? longMonthPrices = null,}) {
  return _then(_Prices(
shortMonthPrices: null == shortMonthPrices ? _self._shortMonthPrices : shortMonthPrices // ignore: cast_nullable_to_non_nullable
as List<int>,longMonthPrices: null == longMonthPrices ? _self._longMonthPrices : longMonthPrices // ignore: cast_nullable_to_non_nullable
as List<int>,
  ));
}


}

// dart format on
