import 'package:freezed_annotation/freezed_annotation.dart';

@JsonEnum()
enum WeekDays {
  @JsonValue('MONDAY')
  monday,
  @JsonValue('TUESDAY')
  tuesday,
  @<PERSON>sonValue('WEDNESDAY')
  wednesday,
  @<PERSON>sonValue('THURSDAY')
  thursday,
  @JsonValue('FRIDAY')
  friday,
  unknown,
}

extension WeekDaysX on WeekDays {
  String toLocalString() => switch (this) {
    WeekDays.monday => 'Lunes',
    WeekDays.tuesday => 'Martes',
    WeekDays.wednesday => 'Miércoles',
    WeekDays.thursday => 'Jueves',
    WeekDays.friday => 'Viernes',
    _ => 'NONE',
  };
}
