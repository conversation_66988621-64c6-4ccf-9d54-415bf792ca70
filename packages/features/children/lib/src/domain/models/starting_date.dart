import 'package:freezed_annotation/freezed_annotation.dart';

@JsonEnum()
enum StartingDate {
  @JsonValue('Septiembre 2024')
  september2024,
  @JsonValue('Octubre 2024')
  obtober2024,
  unknown,
}

extension StartingDateX on StartingDate {
  String getName() => switch (this) {
    StartingDate.september2024 => 'Septiembre 2024',
    StartingDate.obtober2024 => 'Octubre 2024',
    _ => 'NONE',
  };
}
