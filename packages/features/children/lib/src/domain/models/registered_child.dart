import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/models/school_year.dart';
import 'package:children/src/domain/models/week_days.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'registered_child.freezed.dart';
part 'registered_child.g.dart';

@freezed
abstract class RegisteredChild with _$RegisteredChild {
  factory RegisteredChild({
    required String name,
    required String lastName,
    required DateTime dateOfBirth,
    @JsonKey(unknownEnumValue: SchoolYear.unknown)
    required SchoolYear schoolYear,
    required List<String> alergies,
    required Set<WeekDays> weekAssitance,
    required bool hasDinningScholarship,
    required bool isDinningIn,
    required String childId,
    required String parentId,
    @Default(false) bool? isAllowedToExit,
  }) = _RegisteredChild;

  factory RegisteredChild.fromJson(Map<String, dynamic> json) =>
      _$RegisteredChildFromJson(json);

  factory RegisteredChild.fromChild(Child child, String parentId) {
    return RegisteredChild(
      name: child.name,
      lastName: child.lastName,
      dateOfBirth: child.dateOfBirth,
      schoolYear: child.schoolYear,
      alergies: child.alergies,
      weekAssitance: child.weekAssitance,
      hasDinningScholarship: child.hasDinningScholarship,
      isDinningIn: child.isDinningIn,
      childId: child.id!,
      parentId: parentId,
    );
  }
}

extension RegisteredChildX on RegisteredChild {
  String get fullName => '$name $lastName';
}
