// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'child.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Child {

 String get name; String get lastName; DateTime get dateOfBirth;@JsonKey(unknownEnumValue: SchoolYear.unknown) SchoolYear get schoolYear; List<String> get alergies; Set<WeekDays> get weekAssitance; String get avatarUrl; bool get hasAttendendInPreviousCourses; bool get hasSiblingsWhoNeedsDinning; bool get hasDinningScholarship; bool get isDinningIn;@JsonKey(unknownEnumValue: StartingDate.unknown, includeIfNull: true) StartingDate? get startingDate; bool? get isAllowedToExit;@JsonKey(includeToJson: false) String? get id;
/// Create a copy of Child
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChildCopyWith<Child> get copyWith => _$ChildCopyWithImpl<Child>(this as Child, _$identity);

  /// Serializes this Child to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Child&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.schoolYear, schoolYear) || other.schoolYear == schoolYear)&&const DeepCollectionEquality().equals(other.alergies, alergies)&&const DeepCollectionEquality().equals(other.weekAssitance, weekAssitance)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.hasAttendendInPreviousCourses, hasAttendendInPreviousCourses) || other.hasAttendendInPreviousCourses == hasAttendendInPreviousCourses)&&(identical(other.hasSiblingsWhoNeedsDinning, hasSiblingsWhoNeedsDinning) || other.hasSiblingsWhoNeedsDinning == hasSiblingsWhoNeedsDinning)&&(identical(other.hasDinningScholarship, hasDinningScholarship) || other.hasDinningScholarship == hasDinningScholarship)&&(identical(other.isDinningIn, isDinningIn) || other.isDinningIn == isDinningIn)&&(identical(other.startingDate, startingDate) || other.startingDate == startingDate)&&(identical(other.isAllowedToExit, isAllowedToExit) || other.isAllowedToExit == isAllowedToExit)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,lastName,dateOfBirth,schoolYear,const DeepCollectionEquality().hash(alergies),const DeepCollectionEquality().hash(weekAssitance),avatarUrl,hasAttendendInPreviousCourses,hasSiblingsWhoNeedsDinning,hasDinningScholarship,isDinningIn,startingDate,isAllowedToExit,id);

@override
String toString() {
  return 'Child(name: $name, lastName: $lastName, dateOfBirth: $dateOfBirth, schoolYear: $schoolYear, alergies: $alergies, weekAssitance: $weekAssitance, avatarUrl: $avatarUrl, hasAttendendInPreviousCourses: $hasAttendendInPreviousCourses, hasSiblingsWhoNeedsDinning: $hasSiblingsWhoNeedsDinning, hasDinningScholarship: $hasDinningScholarship, isDinningIn: $isDinningIn, startingDate: $startingDate, isAllowedToExit: $isAllowedToExit, id: $id)';
}


}

/// @nodoc
abstract mixin class $ChildCopyWith<$Res>  {
  factory $ChildCopyWith(Child value, $Res Function(Child) _then) = _$ChildCopyWithImpl;
@useResult
$Res call({
 String name, String lastName, DateTime dateOfBirth,@JsonKey(unknownEnumValue: SchoolYear.unknown) SchoolYear schoolYear, List<String> alergies, Set<WeekDays> weekAssitance, String avatarUrl, bool hasAttendendInPreviousCourses, bool hasSiblingsWhoNeedsDinning, bool hasDinningScholarship, bool isDinningIn,@JsonKey(unknownEnumValue: StartingDate.unknown, includeIfNull: true) StartingDate? startingDate, bool? isAllowedToExit,@JsonKey(includeToJson: false) String? id
});




}
/// @nodoc
class _$ChildCopyWithImpl<$Res>
    implements $ChildCopyWith<$Res> {
  _$ChildCopyWithImpl(this._self, this._then);

  final Child _self;
  final $Res Function(Child) _then;

/// Create a copy of Child
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? lastName = null,Object? dateOfBirth = null,Object? schoolYear = null,Object? alergies = null,Object? weekAssitance = null,Object? avatarUrl = null,Object? hasAttendendInPreviousCourses = null,Object? hasSiblingsWhoNeedsDinning = null,Object? hasDinningScholarship = null,Object? isDinningIn = null,Object? startingDate = freezed,Object? isAllowedToExit = freezed,Object? id = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,dateOfBirth: null == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime,schoolYear: null == schoolYear ? _self.schoolYear : schoolYear // ignore: cast_nullable_to_non_nullable
as SchoolYear,alergies: null == alergies ? _self.alergies : alergies // ignore: cast_nullable_to_non_nullable
as List<String>,weekAssitance: null == weekAssitance ? _self.weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Set<WeekDays>,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,hasAttendendInPreviousCourses: null == hasAttendendInPreviousCourses ? _self.hasAttendendInPreviousCourses : hasAttendendInPreviousCourses // ignore: cast_nullable_to_non_nullable
as bool,hasSiblingsWhoNeedsDinning: null == hasSiblingsWhoNeedsDinning ? _self.hasSiblingsWhoNeedsDinning : hasSiblingsWhoNeedsDinning // ignore: cast_nullable_to_non_nullable
as bool,hasDinningScholarship: null == hasDinningScholarship ? _self.hasDinningScholarship : hasDinningScholarship // ignore: cast_nullable_to_non_nullable
as bool,isDinningIn: null == isDinningIn ? _self.isDinningIn : isDinningIn // ignore: cast_nullable_to_non_nullable
as bool,startingDate: freezed == startingDate ? _self.startingDate : startingDate // ignore: cast_nullable_to_non_nullable
as StartingDate?,isAllowedToExit: freezed == isAllowedToExit ? _self.isAllowedToExit : isAllowedToExit // ignore: cast_nullable_to_non_nullable
as bool?,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Child implements Child {
   _Child({required this.name, required this.lastName, required this.dateOfBirth, @JsonKey(unknownEnumValue: SchoolYear.unknown) required this.schoolYear, required final  List<String> alergies, required final  Set<WeekDays> weekAssitance, required this.avatarUrl, required this.hasAttendendInPreviousCourses, required this.hasSiblingsWhoNeedsDinning, required this.hasDinningScholarship, required this.isDinningIn, @JsonKey(unknownEnumValue: StartingDate.unknown, includeIfNull: true) required this.startingDate, this.isAllowedToExit = false, @JsonKey(includeToJson: false) this.id}): _alergies = alergies,_weekAssitance = weekAssitance;
  factory _Child.fromJson(Map<String, dynamic> json) => _$ChildFromJson(json);

@override final  String name;
@override final  String lastName;
@override final  DateTime dateOfBirth;
@override@JsonKey(unknownEnumValue: SchoolYear.unknown) final  SchoolYear schoolYear;
 final  List<String> _alergies;
@override List<String> get alergies {
  if (_alergies is EqualUnmodifiableListView) return _alergies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_alergies);
}

 final  Set<WeekDays> _weekAssitance;
@override Set<WeekDays> get weekAssitance {
  if (_weekAssitance is EqualUnmodifiableSetView) return _weekAssitance;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableSetView(_weekAssitance);
}

@override final  String avatarUrl;
@override final  bool hasAttendendInPreviousCourses;
@override final  bool hasSiblingsWhoNeedsDinning;
@override final  bool hasDinningScholarship;
@override final  bool isDinningIn;
@override@JsonKey(unknownEnumValue: StartingDate.unknown, includeIfNull: true) final  StartingDate? startingDate;
@override@JsonKey() final  bool? isAllowedToExit;
@override@JsonKey(includeToJson: false) final  String? id;

/// Create a copy of Child
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChildCopyWith<_Child> get copyWith => __$ChildCopyWithImpl<_Child>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChildToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Child&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.schoolYear, schoolYear) || other.schoolYear == schoolYear)&&const DeepCollectionEquality().equals(other._alergies, _alergies)&&const DeepCollectionEquality().equals(other._weekAssitance, _weekAssitance)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.hasAttendendInPreviousCourses, hasAttendendInPreviousCourses) || other.hasAttendendInPreviousCourses == hasAttendendInPreviousCourses)&&(identical(other.hasSiblingsWhoNeedsDinning, hasSiblingsWhoNeedsDinning) || other.hasSiblingsWhoNeedsDinning == hasSiblingsWhoNeedsDinning)&&(identical(other.hasDinningScholarship, hasDinningScholarship) || other.hasDinningScholarship == hasDinningScholarship)&&(identical(other.isDinningIn, isDinningIn) || other.isDinningIn == isDinningIn)&&(identical(other.startingDate, startingDate) || other.startingDate == startingDate)&&(identical(other.isAllowedToExit, isAllowedToExit) || other.isAllowedToExit == isAllowedToExit)&&(identical(other.id, id) || other.id == id));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,lastName,dateOfBirth,schoolYear,const DeepCollectionEquality().hash(_alergies),const DeepCollectionEquality().hash(_weekAssitance),avatarUrl,hasAttendendInPreviousCourses,hasSiblingsWhoNeedsDinning,hasDinningScholarship,isDinningIn,startingDate,isAllowedToExit,id);

@override
String toString() {
  return 'Child(name: $name, lastName: $lastName, dateOfBirth: $dateOfBirth, schoolYear: $schoolYear, alergies: $alergies, weekAssitance: $weekAssitance, avatarUrl: $avatarUrl, hasAttendendInPreviousCourses: $hasAttendendInPreviousCourses, hasSiblingsWhoNeedsDinning: $hasSiblingsWhoNeedsDinning, hasDinningScholarship: $hasDinningScholarship, isDinningIn: $isDinningIn, startingDate: $startingDate, isAllowedToExit: $isAllowedToExit, id: $id)';
}


}

/// @nodoc
abstract mixin class _$ChildCopyWith<$Res> implements $ChildCopyWith<$Res> {
  factory _$ChildCopyWith(_Child value, $Res Function(_Child) _then) = __$ChildCopyWithImpl;
@override @useResult
$Res call({
 String name, String lastName, DateTime dateOfBirth,@JsonKey(unknownEnumValue: SchoolYear.unknown) SchoolYear schoolYear, List<String> alergies, Set<WeekDays> weekAssitance, String avatarUrl, bool hasAttendendInPreviousCourses, bool hasSiblingsWhoNeedsDinning, bool hasDinningScholarship, bool isDinningIn,@JsonKey(unknownEnumValue: StartingDate.unknown, includeIfNull: true) StartingDate? startingDate, bool? isAllowedToExit,@JsonKey(includeToJson: false) String? id
});




}
/// @nodoc
class __$ChildCopyWithImpl<$Res>
    implements _$ChildCopyWith<$Res> {
  __$ChildCopyWithImpl(this._self, this._then);

  final _Child _self;
  final $Res Function(_Child) _then;

/// Create a copy of Child
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? lastName = null,Object? dateOfBirth = null,Object? schoolYear = null,Object? alergies = null,Object? weekAssitance = null,Object? avatarUrl = null,Object? hasAttendendInPreviousCourses = null,Object? hasSiblingsWhoNeedsDinning = null,Object? hasDinningScholarship = null,Object? isDinningIn = null,Object? startingDate = freezed,Object? isAllowedToExit = freezed,Object? id = freezed,}) {
  return _then(_Child(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,dateOfBirth: null == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime,schoolYear: null == schoolYear ? _self.schoolYear : schoolYear // ignore: cast_nullable_to_non_nullable
as SchoolYear,alergies: null == alergies ? _self._alergies : alergies // ignore: cast_nullable_to_non_nullable
as List<String>,weekAssitance: null == weekAssitance ? _self._weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Set<WeekDays>,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,hasAttendendInPreviousCourses: null == hasAttendendInPreviousCourses ? _self.hasAttendendInPreviousCourses : hasAttendendInPreviousCourses // ignore: cast_nullable_to_non_nullable
as bool,hasSiblingsWhoNeedsDinning: null == hasSiblingsWhoNeedsDinning ? _self.hasSiblingsWhoNeedsDinning : hasSiblingsWhoNeedsDinning // ignore: cast_nullable_to_non_nullable
as bool,hasDinningScholarship: null == hasDinningScholarship ? _self.hasDinningScholarship : hasDinningScholarship // ignore: cast_nullable_to_non_nullable
as bool,isDinningIn: null == isDinningIn ? _self.isDinningIn : isDinningIn // ignore: cast_nullable_to_non_nullable
as bool,startingDate: freezed == startingDate ? _self.startingDate : startingDate // ignore: cast_nullable_to_non_nullable
as StartingDate?,isAllowedToExit: freezed == isAllowedToExit ? _self.isAllowedToExit : isAllowedToExit // ignore: cast_nullable_to_non_nullable
as bool?,id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
