import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart';

part 'attendance.freezed.dart';
part 'attendance.g.dart';

@freezed
abstract class Attendance with _$Attendance {
  factory Attendance({
    required String childId,
    required String parentId,
    @CustomDateTimeConverter() required DateTime date,
    required bool attendance,
  }) = _Attendance;

  factory Attendance.fromJson(Map<String, dynamic> json) =>
      _$AttendanceFromJson(json);
}

class CustomDateTimeConverter implements JsonConverter<DateTime, String> {
  const CustomDateTimeConverter();

  @override
  DateTime fromJson(String json) {
    return DateFormat('dd-MM-yyyy').parse(json);
  }

  @override
  String toJson(DateTime json) => DateFormat('dd-MM-yyyy').format(json);
}
