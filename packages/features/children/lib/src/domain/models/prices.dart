import 'package:freezed_annotation/freezed_annotation.dart';

part 'prices.freezed.dart';
part 'prices.g.dart';

@freezed
abstract class Prices with _$Prices {
  factory Prices({
    @Default([20, 30, 45, 60, 70]) List<int> shortMonthPrices,
    @Default([25, 45, 65, 90, 110]) List<int> longMonthPrices,
  }) = _Prices;

  factory Prices.fromJson(Map<String, dynamic> json) => _$PricesFromJson(json);
}
