// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auth/auth.dart' as _i662;
import 'package:children/src/di/di_module.dart' as _i489;
import 'package:children/src/domain/models/child.dart' as _i554;
import 'package:children/src/domain/models/registered_child.dart' as _i474;
import 'package:children/src/domain/provider/attendance_provider.dart' as _i679;
import 'package:children/src/domain/provider/child_provider.dart' as _i462;
import 'package:children/src/domain/provider/registered_child_provider.dart'
    as _i137;
import 'package:children/src/domain/repository/attendance_repository.dart'
    as _i823;
import 'package:children/src/domain/repository/child_repository.dart' as _i133;
import 'package:children/src/domain/repository/registered_child_repository.dart'
    as _i971;
import 'package:children/src/domain/usecases/get_children_usecase.dart'
    as _i488;
import 'package:children/src/domain/usecases/get_registered_children_usecase.dart'
    as _i611;
import 'package:children/src/domain/usecases/get_today_attendance_usecase.dart'
    as _i700;
import 'package:children/src/domain/usecases/save_attendance_for_child_usecase.dart'
    as _i192;
import 'package:children/src/domain/usecases/save_bulk_children_usecase.dart'
    as _i199;
import 'package:children/src/domain/usecases/save_child_usecase.dart' as _i314;
import 'package:children/src/domain/usecases/save_today_attendance_usecase.dart'
    as _i867;
import 'package:children/src/domain/usecases/update_child_details_usecase.dart'
    as _i795;
import 'package:children/src/navigator/children_navigator.dart' as _i404;
import 'package:children/src/presentation/all_children_report/bloc/all_children_report_bloc.dart'
    as _i926;
import 'package:children/src/presentation/attendance/bloc/attendance_bloc.dart'
    as _i886;
import 'package:children/src/presentation/child_details/bloc/child_details_bloc.dart'
    as _i121;
import 'package:children/src/presentation/children_list/bloc/children_list_bloc.dart'
    as _i805;
import 'package:children/src/presentation/manager_children_list/bloc/manager_children_list_bloc.dart'
    as _i445;
import 'package:children/src/presentation/register_child/bloc/register_child_bloc.dart'
    as _i568;
import 'package:children/src/presentation/registered_children_list/bloc/registered_children_list_bloc.dart'
    as _i736;
import 'package:cloud_firestore/cloud_firestore.dart' as _i974;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:shared_preferences/shared_preferences.dart' as _i460;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    final diModule = _$DiModule();
    await gh.factoryAsync<_i460.SharedPreferences>(
      () => diModule.prefs,
      preResolve: true,
    );
    gh.factory<_i137.RegisteredChildProvider>(
      () => _i137.RegisteredChildProvider(gh<_i974.FirebaseFirestore>()),
    );
    gh.factory<_i462.ChildProvider>(
      () => _i462.ChildProvider(gh<_i974.FirebaseFirestore>()),
    );
    gh.factory<_i971.RegisteredChildRepository>(
      () =>
          _i971.RegisteredChildRepository(gh<_i137.RegisteredChildProvider>()),
    );
    gh.factory<_i133.ChildRepository>(
      () => _i133.ChildRepository(gh<_i462.ChildProvider>()),
    );
    gh.factory<_i679.AttendanceProvider>(
      () => _i679.AttendanceProvider(
        gh<_i974.FirebaseFirestore>(),
        gh<_i460.SharedPreferences>(),
      ),
    );
    gh.factory<_i823.AttendanceRepository>(
      () => _i823.AttendanceRepository(gh<_i679.AttendanceProvider>()),
    );
    gh.factory<_i867.SaveTodayAttendanceUsecase>(
      () => _i867.SaveTodayAttendanceUsecase(gh<_i823.AttendanceRepository>()),
    );
    gh.factory<_i700.GetTodayAttendanceUsecase>(
      () => _i700.GetTodayAttendanceUsecase(gh<_i823.AttendanceRepository>()),
    );
    gh.factory<_i314.SaveChildUsecase>(
      () => _i314.SaveChildUsecase(gh<_i133.ChildRepository>()),
    );
    gh.factory<_i795.UpdateChildDetailsUsecase>(
      () => _i795.UpdateChildDetailsUsecase(gh<_i133.ChildRepository>()),
    );
    gh.factory<_i488.GetChildrenUsecase>(
      () => _i488.GetChildrenUsecase(gh<_i133.ChildRepository>()),
    );
    gh.factory<_i192.SaveAttendanceForChildUsecase>(
      () =>
          _i192.SaveAttendanceForChildUsecase(gh<_i823.AttendanceRepository>()),
    );
    gh.factory<_i199.SaveBulkChildrenUsecase>(
      () =>
          _i199.SaveBulkChildrenUsecase(gh<_i971.RegisteredChildRepository>()),
    );
    gh.factory<_i611.GetRegisteredChildrenUsecase>(
      () => _i611.GetRegisteredChildrenUsecase(
        gh<_i971.RegisteredChildRepository>(),
      ),
    );
    gh.factory<_i805.ChildrenListBloc>(
      () => _i805.ChildrenListBloc(
        gh<_i488.GetChildrenUsecase>(),
        gh<_i662.GetCurrentUserUsecase>(),
        gh<_i404.ChildrenNavigator>(),
        gh<_i662.SignOutUsecase>(),
      ),
    );
    gh.factory<_i445.ManagerChildrenListBloc>(
      () => _i445.ManagerChildrenListBloc(
        gh<_i488.GetChildrenUsecase>(),
        gh<_i662.GetAllUsersUsecase>(),
      ),
    );
    gh.factoryParam<_i121.ChildDetailsBloc, _i554.Child, dynamic>(
      (child, _) => _i121.ChildDetailsBloc(
        child,
        gh<_i795.UpdateChildDetailsUsecase>(),
        gh<_i662.GetCurrentUserUsecase>(),
      ),
    );
    gh.factory<_i736.RegisteredChildrenListBloc>(
      () => _i736.RegisteredChildrenListBloc(
        gh<_i611.GetRegisteredChildrenUsecase>(),
        gh<_i700.GetTodayAttendanceUsecase>(),
        gh<_i867.SaveTodayAttendanceUsecase>(),
      ),
    );
    gh.factory<_i568.RegisterChildBloc>(
      () => _i568.RegisterChildBloc(
        gh<_i314.SaveChildUsecase>(),
        gh<_i662.GetCurrentUserUsecase>(),
        gh<_i404.ChildrenNavigator>(),
      ),
    );
    gh.factoryParam<_i886.AttendanceBloc, _i474.RegisteredChild, dynamic>(
      (child, _) => _i886.AttendanceBloc(
        gh<_i192.SaveAttendanceForChildUsecase>(),
        child,
      ),
    );
    gh.factory<_i926.AllChildrenReportBloc>(
      () => _i926.AllChildrenReportBloc(
        gh<_i662.GetAllUsersUsecase>(),
        gh<_i488.GetChildrenUsecase>(),
        gh<_i199.SaveBulkChildrenUsecase>(),
      ),
    );
    return this;
  }
}

class _$DiModule extends _i489.DiModule {}
