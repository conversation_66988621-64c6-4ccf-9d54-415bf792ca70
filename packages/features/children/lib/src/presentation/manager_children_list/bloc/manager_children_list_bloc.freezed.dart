// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manager_children_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ManagerChildrenListEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ManagerChildrenListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ManagerChildrenListEvent()';
}


}

/// @nodoc
class $ManagerChildrenListEventCopyWith<$Res>  {
$ManagerChildrenListEventCopyWith(ManagerChildrenListEvent _, $Res Function(ManagerChildrenListEvent) __);
}


/// @nodoc


class _LoadManagerChildrenListEvent implements ManagerChildrenListEvent {
  const _LoadManagerChildrenListEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadManagerChildrenListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ManagerChildrenListEvent.load()';
}


}




/// @nodoc
mixin _$ManagerChildrenListState {

 List<Child> get children; ProcessState get status;
/// Create a copy of ManagerChildrenListState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ManagerChildrenListStateCopyWith<ManagerChildrenListState> get copyWith => _$ManagerChildrenListStateCopyWithImpl<ManagerChildrenListState>(this as ManagerChildrenListState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ManagerChildrenListState&&const DeepCollectionEquality().equals(other.children, children)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(children),status);

@override
String toString() {
  return 'ManagerChildrenListState(children: $children, status: $status)';
}


}

/// @nodoc
abstract mixin class $ManagerChildrenListStateCopyWith<$Res>  {
  factory $ManagerChildrenListStateCopyWith(ManagerChildrenListState value, $Res Function(ManagerChildrenListState) _then) = _$ManagerChildrenListStateCopyWithImpl;
@useResult
$Res call({
 List<Child> children, ProcessState status
});


$ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class _$ManagerChildrenListStateCopyWithImpl<$Res>
    implements $ManagerChildrenListStateCopyWith<$Res> {
  _$ManagerChildrenListStateCopyWithImpl(this._self, this._then);

  final ManagerChildrenListState _self;
  final $Res Function(ManagerChildrenListState) _then;

/// Create a copy of ManagerChildrenListState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? children = null,Object? status = null,}) {
  return _then(_self.copyWith(
children: null == children ? _self.children : children // ignore: cast_nullable_to_non_nullable
as List<Child>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,
  ));
}
/// Create a copy of ManagerChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}


/// @nodoc


class _ManagerChildrenListState implements ManagerChildrenListState {
  const _ManagerChildrenListState({final  List<Child> children = const [], this.status = const ProcessState.initial()}): _children = children;
  

 final  List<Child> _children;
@override@JsonKey() List<Child> get children {
  if (_children is EqualUnmodifiableListView) return _children;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_children);
}

@override@JsonKey() final  ProcessState status;

/// Create a copy of ManagerChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ManagerChildrenListStateCopyWith<_ManagerChildrenListState> get copyWith => __$ManagerChildrenListStateCopyWithImpl<_ManagerChildrenListState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ManagerChildrenListState&&const DeepCollectionEquality().equals(other._children, _children)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_children),status);

@override
String toString() {
  return 'ManagerChildrenListState(children: $children, status: $status)';
}


}

/// @nodoc
abstract mixin class _$ManagerChildrenListStateCopyWith<$Res> implements $ManagerChildrenListStateCopyWith<$Res> {
  factory _$ManagerChildrenListStateCopyWith(_ManagerChildrenListState value, $Res Function(_ManagerChildrenListState) _then) = __$ManagerChildrenListStateCopyWithImpl;
@override @useResult
$Res call({
 List<Child> children, ProcessState status
});


@override $ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class __$ManagerChildrenListStateCopyWithImpl<$Res>
    implements _$ManagerChildrenListStateCopyWith<$Res> {
  __$ManagerChildrenListStateCopyWithImpl(this._self, this._then);

  final _ManagerChildrenListState _self;
  final $Res Function(_ManagerChildrenListState) _then;

/// Create a copy of ManagerChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? children = null,Object? status = null,}) {
  return _then(_ManagerChildrenListState(
children: null == children ? _self._children : children // ignore: cast_nullable_to_non_nullable
as List<Child>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,
  ));
}

/// Create a copy of ManagerChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}

// dart format on
