import 'package:auth/auth.dart';
import 'package:bloc/bloc.dart';
import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/usecases/get_children_usecase.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'manager_children_list_event.dart';
part 'manager_children_list_state.dart';
part 'manager_children_list_bloc.freezed.dart';

@injectable
class ManagerChildrenListBloc
    extends Bloc<ManagerChildrenListEvent, ManagerChildrenListState> {
  ManagerChildrenListBloc(
    this._getChildrenUsecase,
    this._getAllUsersUsecase,
  ) : super(const _ManagerChildrenListState()) {
    on<ManagerChildrenListEvent>(
      (event, emit) => switch (event) {
        _LoadManagerChildrenListEvent() => _onLoad(emit, event),
        _ => null,
      },
    );

    add(const ManagerChildrenListEvent.load());
  }

  final GetChildrenUsecase _getChildrenUsecase;
  final GetAllUsersUsecase _getAllUsersUsecase;

  Future<void> _onLoad(
    Emitter<ManagerChildrenListState> emit,
    ManagerChildrenListEvent event,
  ) async {
    final chidren = <Child>[];
    emit(state.copyWith(status: const ProcessState.loading()));

    final allUsers = (await _getAllUsersUsecase()).getOrNull();
    if (allUsers == null) {
      emit(state.copyWith(status: ProcessState.error(Exception(''))));
      return;
    }
    for (final user in allUsers) {
      chidren.addAll((await _getChildrenUsecase(user.id!)).getOrElse(() => []));
    }

    emit(
      state.copyWith(children: chidren, status: const ProcessState.loaded()),
    );
  }
}
