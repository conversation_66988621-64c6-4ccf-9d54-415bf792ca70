import 'package:app_di/app_di.dart';
import 'package:children/src/presentation/manager_children_list/bloc/manager_children_list_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ManagerChildrenListPage extends StatelessWidget {
  const ManagerChildrenListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocProvider(
        create: (_) => diContainer<ManagerChildrenListBloc>(),
        child: const _Content(),
      ),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ManagerChildrenListBloc, ManagerChildrenListState>(
      builder: (context, state) => switch (state.status) {
        InitialProcessState() => const Center(
          child: CircularProgressIndicator(),
        ),
        LoadingProcessState() => const Center(
          child: CircularProgressIndicator(),
        ),
        LoadedProcessState() => ListView.builder(
          itemCount: state.children.length,
          itemBuilder: (context, index) {
            final child = state.children[index];
            return ListTile(
              title: Text(
                '${child.name.toCapitalize()} ${child.lastName.toCapitalize()}',
              ),
            );
          },
        ),
        ErrorProcessState() => const Center(child: Text('Error')),
      },
    );
  }
}
