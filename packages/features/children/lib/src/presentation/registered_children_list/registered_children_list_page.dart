import 'package:app_di/app_di.dart';
import 'package:auth/auth.dart';
import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/presentation/registered_children_list/bloc/registered_children_list_bloc.dart';
import 'package:children/src/presentation/registered_children_list/widgets/child_list_tile.dart';
import 'package:children/src/presentation/registered_children_list/widgets/registered_children_search_delegate.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:prelude/prelude.dart';

class RegisteredChildrenListPage extends StatelessWidget {
  const RegisteredChildrenListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => diContainer<RegisteredChildrenListBloc>(),
      child: const _Scaffold(),
    );
  }
}

class _Scaffold extends StatelessWidget {
  const _Scaffold();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          children: [
            const Text('Asistencia'),
            Text(
              DateFormat(
                DateFormat.ABBR_MONTH_WEEKDAY_DAY,
                'es',
              ).format(DateTime.now()),
              style: Theme.of(context).appTextTheme.body2,
            ),
          ],
        ),
        actions: [
          BlocBuilder<RegisteredChildrenListBloc, RegisteredChildrenListState>(
            builder: (context, state) {
              if (state.isSearching) {
                return IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => context
                      .read<RegisteredChildrenListBloc>()
                      .add(const RegisteredChildrenListEvent.search(false)),
                );
              }
              return IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  context.read<RegisteredChildrenListBloc>().add(
                    const RegisteredChildrenListEvent.search(true),
                  );
                  showSearch(
                    context: context,
                    delegate: RegisteredChildSearchDelegate(
                      bloc: context.read<RegisteredChildrenListBloc>(),
                      children: state.children,
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
      body: const _Content(),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RegisteredChildrenListBloc, RegisteredChildrenListState>(
      builder: (context, state) => switch (state.status) {
        LoadedProcessState() => _ChildrenListView(children: state.children),
        _ => const LoadingView(),
      },
    );
  }
}

class _ChildrenListView extends StatelessWidget {
  const _ChildrenListView({required this.children});

  final List<RegisteredChild> children;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: children.length,
      itemBuilder: (context, index) {
        final child = children.elementAt(index);
        return ChildListTile(
          key: ValueKey(child.childId),
          bloc: context.read<RegisteredChildrenListBloc>(),
          child: child,
        );
      },
    );
  }
}
