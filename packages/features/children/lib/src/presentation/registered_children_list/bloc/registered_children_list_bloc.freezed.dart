// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'registered_children_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$RegisteredChildrenListEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisteredChildrenListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RegisteredChildrenListEvent()';
}


}

/// @nodoc
class $RegisteredChildrenListEventCopyWith<$Res>  {
$RegisteredChildrenListEventCopyWith(RegisteredChildrenListEvent _, $Res Function(RegisteredChildrenListEvent) __);
}


/// @nodoc


class _LoadRegisteredChildrenListEvent implements RegisteredChildrenListEvent {
  const _LoadRegisteredChildrenListEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadRegisteredChildrenListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RegisteredChildrenListEvent.load()';
}


}




/// @nodoc


class _SetAssistanceChildrenListEvent implements RegisteredChildrenListEvent {
  const _SetAssistanceChildrenListEvent(this.child, this.assistance);
  

 final  RegisteredChild child;
 final  bool assistance;

/// Create a copy of RegisteredChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetAssistanceChildrenListEventCopyWith<_SetAssistanceChildrenListEvent> get copyWith => __$SetAssistanceChildrenListEventCopyWithImpl<_SetAssistanceChildrenListEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetAssistanceChildrenListEvent&&(identical(other.child, child) || other.child == child)&&(identical(other.assistance, assistance) || other.assistance == assistance));
}


@override
int get hashCode => Object.hash(runtimeType,child,assistance);

@override
String toString() {
  return 'RegisteredChildrenListEvent.setAssistance(child: $child, assistance: $assistance)';
}


}

/// @nodoc
abstract mixin class _$SetAssistanceChildrenListEventCopyWith<$Res> implements $RegisteredChildrenListEventCopyWith<$Res> {
  factory _$SetAssistanceChildrenListEventCopyWith(_SetAssistanceChildrenListEvent value, $Res Function(_SetAssistanceChildrenListEvent) _then) = __$SetAssistanceChildrenListEventCopyWithImpl;
@useResult
$Res call({
 RegisteredChild child, bool assistance
});


$RegisteredChildCopyWith<$Res> get child;

}
/// @nodoc
class __$SetAssistanceChildrenListEventCopyWithImpl<$Res>
    implements _$SetAssistanceChildrenListEventCopyWith<$Res> {
  __$SetAssistanceChildrenListEventCopyWithImpl(this._self, this._then);

  final _SetAssistanceChildrenListEvent _self;
  final $Res Function(_SetAssistanceChildrenListEvent) _then;

/// Create a copy of RegisteredChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? child = null,Object? assistance = null,}) {
  return _then(_SetAssistanceChildrenListEvent(
null == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as RegisteredChild,null == assistance ? _self.assistance : assistance // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of RegisteredChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RegisteredChildCopyWith<$Res> get child {
  
  return $RegisteredChildCopyWith<$Res>(_self.child, (value) {
    return _then(_self.copyWith(child: value));
  });
}
}

/// @nodoc


class _SearchChildChildrenListEvent implements RegisteredChildrenListEvent {
  const _SearchChildChildrenListEvent(this.query);
  

 final  String query;

/// Create a copy of RegisteredChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SearchChildChildrenListEventCopyWith<_SearchChildChildrenListEvent> get copyWith => __$SearchChildChildrenListEventCopyWithImpl<_SearchChildChildrenListEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SearchChildChildrenListEvent&&(identical(other.query, query) || other.query == query));
}


@override
int get hashCode => Object.hash(runtimeType,query);

@override
String toString() {
  return 'RegisteredChildrenListEvent.searchChild(query: $query)';
}


}

/// @nodoc
abstract mixin class _$SearchChildChildrenListEventCopyWith<$Res> implements $RegisteredChildrenListEventCopyWith<$Res> {
  factory _$SearchChildChildrenListEventCopyWith(_SearchChildChildrenListEvent value, $Res Function(_SearchChildChildrenListEvent) _then) = __$SearchChildChildrenListEventCopyWithImpl;
@useResult
$Res call({
 String query
});




}
/// @nodoc
class __$SearchChildChildrenListEventCopyWithImpl<$Res>
    implements _$SearchChildChildrenListEventCopyWith<$Res> {
  __$SearchChildChildrenListEventCopyWithImpl(this._self, this._then);

  final _SearchChildChildrenListEvent _self;
  final $Res Function(_SearchChildChildrenListEvent) _then;

/// Create a copy of RegisteredChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? query = null,}) {
  return _then(_SearchChildChildrenListEvent(
null == query ? _self.query : query // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _SearchUIChildrenListEvent implements RegisteredChildrenListEvent {
  const _SearchUIChildrenListEvent(this.isSearching);
  

 final  bool isSearching;

/// Create a copy of RegisteredChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SearchUIChildrenListEventCopyWith<_SearchUIChildrenListEvent> get copyWith => __$SearchUIChildrenListEventCopyWithImpl<_SearchUIChildrenListEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SearchUIChildrenListEvent&&(identical(other.isSearching, isSearching) || other.isSearching == isSearching));
}


@override
int get hashCode => Object.hash(runtimeType,isSearching);

@override
String toString() {
  return 'RegisteredChildrenListEvent.search(isSearching: $isSearching)';
}


}

/// @nodoc
abstract mixin class _$SearchUIChildrenListEventCopyWith<$Res> implements $RegisteredChildrenListEventCopyWith<$Res> {
  factory _$SearchUIChildrenListEventCopyWith(_SearchUIChildrenListEvent value, $Res Function(_SearchUIChildrenListEvent) _then) = __$SearchUIChildrenListEventCopyWithImpl;
@useResult
$Res call({
 bool isSearching
});




}
/// @nodoc
class __$SearchUIChildrenListEventCopyWithImpl<$Res>
    implements _$SearchUIChildrenListEventCopyWith<$Res> {
  __$SearchUIChildrenListEventCopyWithImpl(this._self, this._then);

  final _SearchUIChildrenListEvent _self;
  final $Res Function(_SearchUIChildrenListEvent) _then;

/// Create a copy of RegisteredChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isSearching = null,}) {
  return _then(_SearchUIChildrenListEvent(
null == isSearching ? _self.isSearching : isSearching // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc
mixin _$RegisteredChildrenListState {

 List<RegisteredChild> get children; ProcessState get status; bool get isSearching; Map<String, bool?> get assistance;
/// Create a copy of RegisteredChildrenListState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RegisteredChildrenListStateCopyWith<RegisteredChildrenListState> get copyWith => _$RegisteredChildrenListStateCopyWithImpl<RegisteredChildrenListState>(this as RegisteredChildrenListState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisteredChildrenListState&&const DeepCollectionEquality().equals(other.children, children)&&(identical(other.status, status) || other.status == status)&&(identical(other.isSearching, isSearching) || other.isSearching == isSearching)&&const DeepCollectionEquality().equals(other.assistance, assistance));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(children),status,isSearching,const DeepCollectionEquality().hash(assistance));

@override
String toString() {
  return 'RegisteredChildrenListState(children: $children, status: $status, isSearching: $isSearching, assistance: $assistance)';
}


}

/// @nodoc
abstract mixin class $RegisteredChildrenListStateCopyWith<$Res>  {
  factory $RegisteredChildrenListStateCopyWith(RegisteredChildrenListState value, $Res Function(RegisteredChildrenListState) _then) = _$RegisteredChildrenListStateCopyWithImpl;
@useResult
$Res call({
 List<RegisteredChild> children, ProcessState status, bool isSearching, Map<String, bool?> assistance
});


$ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class _$RegisteredChildrenListStateCopyWithImpl<$Res>
    implements $RegisteredChildrenListStateCopyWith<$Res> {
  _$RegisteredChildrenListStateCopyWithImpl(this._self, this._then);

  final RegisteredChildrenListState _self;
  final $Res Function(RegisteredChildrenListState) _then;

/// Create a copy of RegisteredChildrenListState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? children = null,Object? status = null,Object? isSearching = null,Object? assistance = null,}) {
  return _then(_self.copyWith(
children: null == children ? _self.children : children // ignore: cast_nullable_to_non_nullable
as List<RegisteredChild>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,isSearching: null == isSearching ? _self.isSearching : isSearching // ignore: cast_nullable_to_non_nullable
as bool,assistance: null == assistance ? _self.assistance : assistance // ignore: cast_nullable_to_non_nullable
as Map<String, bool?>,
  ));
}
/// Create a copy of RegisteredChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}


/// @nodoc


class _RegisteredChildrenListState implements RegisteredChildrenListState {
  const _RegisteredChildrenListState({final  List<RegisteredChild> children = const [], this.status = const ProcessState.initial(), this.isSearching = false, final  Map<String, bool?> assistance = const {}}): _children = children,_assistance = assistance;
  

 final  List<RegisteredChild> _children;
@override@JsonKey() List<RegisteredChild> get children {
  if (_children is EqualUnmodifiableListView) return _children;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_children);
}

@override@JsonKey() final  ProcessState status;
@override@JsonKey() final  bool isSearching;
 final  Map<String, bool?> _assistance;
@override@JsonKey() Map<String, bool?> get assistance {
  if (_assistance is EqualUnmodifiableMapView) return _assistance;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_assistance);
}


/// Create a copy of RegisteredChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RegisteredChildrenListStateCopyWith<_RegisteredChildrenListState> get copyWith => __$RegisteredChildrenListStateCopyWithImpl<_RegisteredChildrenListState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RegisteredChildrenListState&&const DeepCollectionEquality().equals(other._children, _children)&&(identical(other.status, status) || other.status == status)&&(identical(other.isSearching, isSearching) || other.isSearching == isSearching)&&const DeepCollectionEquality().equals(other._assistance, _assistance));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_children),status,isSearching,const DeepCollectionEquality().hash(_assistance));

@override
String toString() {
  return 'RegisteredChildrenListState(children: $children, status: $status, isSearching: $isSearching, assistance: $assistance)';
}


}

/// @nodoc
abstract mixin class _$RegisteredChildrenListStateCopyWith<$Res> implements $RegisteredChildrenListStateCopyWith<$Res> {
  factory _$RegisteredChildrenListStateCopyWith(_RegisteredChildrenListState value, $Res Function(_RegisteredChildrenListState) _then) = __$RegisteredChildrenListStateCopyWithImpl;
@override @useResult
$Res call({
 List<RegisteredChild> children, ProcessState status, bool isSearching, Map<String, bool?> assistance
});


@override $ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class __$RegisteredChildrenListStateCopyWithImpl<$Res>
    implements _$RegisteredChildrenListStateCopyWith<$Res> {
  __$RegisteredChildrenListStateCopyWithImpl(this._self, this._then);

  final _RegisteredChildrenListState _self;
  final $Res Function(_RegisteredChildrenListState) _then;

/// Create a copy of RegisteredChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? children = null,Object? status = null,Object? isSearching = null,Object? assistance = null,}) {
  return _then(_RegisteredChildrenListState(
children: null == children ? _self._children : children // ignore: cast_nullable_to_non_nullable
as List<RegisteredChild>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,isSearching: null == isSearching ? _self.isSearching : isSearching // ignore: cast_nullable_to_non_nullable
as bool,assistance: null == assistance ? _self._assistance : assistance // ignore: cast_nullable_to_non_nullable
as Map<String, bool?>,
  ));
}

/// Create a copy of RegisteredChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}

// dart format on
