part of 'registered_children_list_bloc.dart';

@freezed
abstract class RegisteredChildrenListState with _$RegisteredChildrenListState {
  const factory RegisteredChildrenListState({
    @Default([]) List<RegisteredChild> children,
    @Default(ProcessState.initial()) ProcessState status,
    @Default(false) bool isSearching,
    @Default({}) Map<String, bool?> assistance,
  }) = _RegisteredChildrenListState;
}

extension RegisteredChildrenListStateX on RegisteredChildrenListState {
  bool? isChildAssisted(RegisteredChild child) {
    if (assistance[child.childId] == null) {
      return null;
    }
    return assistance[child.childId] == true;
  }
}
