part of 'registered_children_list_bloc.dart';

@freezed
abstract class RegisteredChildrenListEvent with _$RegisteredChildrenListEvent {
  const factory RegisteredChildrenListEvent.load() =
      _LoadRegisteredChildrenListEvent;

  const factory RegisteredChildrenListEvent.setAssistance(
    RegisteredChild child,
    bool assistance,
  ) = _SetAssistanceChildrenListEvent;

  const factory RegisteredChildrenListEvent.searchChild(String query) =
      _SearchChildChildrenListEvent;

  const factory RegisteredChildrenListEvent.search(bool isSearching) =
      _SearchUIChildrenListEvent;
}
