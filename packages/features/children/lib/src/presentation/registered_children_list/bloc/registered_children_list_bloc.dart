import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/domain/usecases/get_registered_children_usecase.dart';
import 'package:children/src/domain/usecases/get_today_attendance_usecase.dart';
import 'package:children/src/domain/usecases/save_today_attendance_usecase.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'registered_children_list_event.dart';
part 'registered_children_list_state.dart';
part 'registered_children_list_bloc.freezed.dart';

@injectable
class RegisteredChildrenListBloc
    extends Bloc<RegisteredChildrenListEvent, RegisteredChildrenListState> {
  RegisteredChildrenListBloc(
    this._getRegisteredChildrenUsecase,
    this._getTodayAttendanceUsecase,
    this._saveTodayAttendanceUsecase,
  ) : super(const RegisteredChildrenListState()) {
    on<_LoadRegisteredChildrenListEvent>(_onLoad);
    on<_SearchUIChildrenListEvent>(_onSearchUI);
    on<_SetAssistanceChildrenListEvent>(_onSetAssistance);

    add(const RegisteredChildrenListEvent.load());
  }

  final GetRegisteredChildrenUsecase _getRegisteredChildrenUsecase;
  final GetTodayAttendanceUsecase _getTodayAttendanceUsecase;
  final SaveTodayAttendanceUsecase _saveTodayAttendanceUsecase;

  Future<void> _onLoad(
    _LoadRegisteredChildrenListEvent event,
    Emitter<RegisteredChildrenListState> emit,
  ) async {
    emit(state.copyWith(status: const ProcessState.loading()));

    final (response, todayAttendance) = await (
      _getRegisteredChildrenUsecase(),
      _getTodayAttendanceUsecase(),
    ).wait;

    response.fold(
      (data) {
        final formattedData =
            data
                .map(
                  (e) => e.copyWith(
                    name: e.name.toCapitalize(),
                    lastName: e.lastName.toCapitalize(),
                  ),
                )
                .toList()
              ..sort((a, b) => a.lastName.compareTo(b.lastName));

        emit(
          state.copyWith(
            status: const ProcessState.loaded(),
            children: formattedData,
            assistance: todayAttendance.fold(
              (data) => data,
              (_) => {},
            ),
          ),
        );
      },
      (e) => emit(
        state.copyWith(
          status: ProcessState.error(e),
        ),
      ),
    );
  }

  Future<void> _onSearchUI(
    _SearchUIChildrenListEvent event,
    Emitter<RegisteredChildrenListState> emit,
  ) async {
    emit(state.copyWith(isSearching: event.isSearching));
  }

  Future<void> _onSetAssistance(
    _SetAssistanceChildrenListEvent event,
    Emitter<RegisteredChildrenListState> emit,
  ) async {
    final current = state.assistance;

    _saveTodayAttendanceUsecase({
      ...current,
      event.child.childId: event.assistance,
    });

    emit(
      state.copyWith(
        assistance: {
          ...current,
          event.child.childId: event.assistance,
        },
      ),
    );
  }
}
