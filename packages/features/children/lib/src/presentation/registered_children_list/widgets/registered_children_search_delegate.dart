import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/presentation/registered_children_list/bloc/registered_children_list_bloc.dart';
import 'package:children/src/presentation/registered_children_list/widgets/child_list_tile.dart';
import 'package:flutter/material.dart';

class RegisteredChildSearchDelegate extends SearchDelegate<RegisteredChild> {
  RegisteredChildSearchDelegate({
    required this.children,
    required this.bloc,
  });

  final List<RegisteredChild> children;
  final RegisteredChildrenListBloc bloc;

  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, children.first);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final results = children
        .where(
          (child) => child.fullName.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();

    return ListView.builder(
      itemCount: results.length,
      itemBuilder: (context, index) {
        final child = results[index];
        return ChildListTile(
          key: ValueKey(child.childId),
          child: child,
          bloc: bloc,
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    final suggestions = children
        .where(
          (child) => child.fullName.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();

    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final child = suggestions[index];
        return ChildListTile(
          key: ValueKey(child.childId),
          child: child,
          bloc: bloc,
        );
      },
    );
  }

  @override
  void close(BuildContext context, RegisteredChild result) {
    bloc.add(
      const RegisteredChildrenListEvent.search(false),
    );
    super.close(context, result);
  }

  @override
  String? get searchFieldLabel => 'Busca un alumno';
}
