import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/domain/models/school_year.dart';
import 'package:children/src/presentation/attendance/attendance_widget.dart';
import 'package:children/src/presentation/registered_children_list/bloc/registered_children_list_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChildListTile extends StatelessWidget {
  const ChildListTile({
    required this.child,
    required this.bloc,
    super.key,
  });

  final RegisteredChild child;
  final RegisteredChildrenListBloc bloc;

  Color isAssistedAvatarColor(BuildContext context, bool? isAssisted) {
    return switch (isAssisted) {
      true => Theme.of(context).colorScheme.primary,
      false => Theme.of(context).colorScheme.error,
      _ => Colors.grey,
    };
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: bloc,
      child:
          BlocBuilder<RegisteredChildrenListBloc, RegisteredChildrenListState>(
            builder: (context, state) {
              return ListTile(
                title: Text('${child.fullName}'),
                subtitle: Text(
                  '${child.schoolYear.getName(true)}',
                  style: Theme.of(context).appTextTheme.label3,
                ),
                leading: CircleAvatar(
                  backgroundColor: isAssistedAvatarColor(
                    context,
                    state.isChildAssisted(child),
                  ),
                  child: const Icon(Icons.person),
                ),
                onTap: () => _showBottomSheetAttendance(context, child),
              );
            },
          ),
    );
  }

  void _showBottomSheetAttendance(
    BuildContext context,
    RegisteredChild child,
  ) {
    final bloc = context.read<RegisteredChildrenListBloc>();
    showModalBottomSheet(
      context: context,
      showDragHandle: true,
      builder: (context) => AttendanceWidget(
        child: child,
        onAttendance: (assistance) => bloc.add(
          RegisteredChildrenListEvent.setAssistance(
            child,
            assistance,
          ),
        ),
      ),
    );
  }
}
