// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'attendance_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AttendanceEvent {

 bool get attendace;
/// Create a copy of AttendanceEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AttendanceEventCopyWith<AttendanceEvent> get copyWith => _$AttendanceEventCopyWithImpl<AttendanceEvent>(this as AttendanceEvent, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AttendanceEvent&&(identical(other.attendace, attendace) || other.attendace == attendace));
}


@override
int get hashCode => Object.hash(runtimeType,attendace);

@override
String toString() {
  return 'AttendanceEvent(attendace: $attendace)';
}


}

/// @nodoc
abstract mixin class $AttendanceEventCopyWith<$Res>  {
  factory $AttendanceEventCopyWith(AttendanceEvent value, $Res Function(AttendanceEvent) _then) = _$AttendanceEventCopyWithImpl;
@useResult
$Res call({
 bool attendace
});




}
/// @nodoc
class _$AttendanceEventCopyWithImpl<$Res>
    implements $AttendanceEventCopyWith<$Res> {
  _$AttendanceEventCopyWithImpl(this._self, this._then);

  final AttendanceEvent _self;
  final $Res Function(AttendanceEvent) _then;

/// Create a copy of AttendanceEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? attendace = null,}) {
  return _then(_self.copyWith(
attendace: null == attendace ? _self.attendace : attendace // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc


class _SetAttendanceAttendanceEvent implements AttendanceEvent {
  const _SetAttendanceAttendanceEvent({this.attendace = true});
  

@override@JsonKey() final  bool attendace;

/// Create a copy of AttendanceEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetAttendanceAttendanceEventCopyWith<_SetAttendanceAttendanceEvent> get copyWith => __$SetAttendanceAttendanceEventCopyWithImpl<_SetAttendanceAttendanceEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetAttendanceAttendanceEvent&&(identical(other.attendace, attendace) || other.attendace == attendace));
}


@override
int get hashCode => Object.hash(runtimeType,attendace);

@override
String toString() {
  return 'AttendanceEvent.setAttendance(attendace: $attendace)';
}


}

/// @nodoc
abstract mixin class _$SetAttendanceAttendanceEventCopyWith<$Res> implements $AttendanceEventCopyWith<$Res> {
  factory _$SetAttendanceAttendanceEventCopyWith(_SetAttendanceAttendanceEvent value, $Res Function(_SetAttendanceAttendanceEvent) _then) = __$SetAttendanceAttendanceEventCopyWithImpl;
@override @useResult
$Res call({
 bool attendace
});




}
/// @nodoc
class __$SetAttendanceAttendanceEventCopyWithImpl<$Res>
    implements _$SetAttendanceAttendanceEventCopyWith<$Res> {
  __$SetAttendanceAttendanceEventCopyWithImpl(this._self, this._then);

  final _SetAttendanceAttendanceEvent _self;
  final $Res Function(_SetAttendanceAttendanceEvent) _then;

/// Create a copy of AttendanceEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? attendace = null,}) {
  return _then(_SetAttendanceAttendanceEvent(
attendace: null == attendace ? _self.attendace : attendace // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc
mixin _$AttendanceState {

 RegisteredChild get child;
/// Create a copy of AttendanceState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AttendanceStateCopyWith<AttendanceState> get copyWith => _$AttendanceStateCopyWithImpl<AttendanceState>(this as AttendanceState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AttendanceState&&(identical(other.child, child) || other.child == child));
}


@override
int get hashCode => Object.hash(runtimeType,child);

@override
String toString() {
  return 'AttendanceState(child: $child)';
}


}

/// @nodoc
abstract mixin class $AttendanceStateCopyWith<$Res>  {
  factory $AttendanceStateCopyWith(AttendanceState value, $Res Function(AttendanceState) _then) = _$AttendanceStateCopyWithImpl;
@useResult
$Res call({
 RegisteredChild child
});


$RegisteredChildCopyWith<$Res> get child;

}
/// @nodoc
class _$AttendanceStateCopyWithImpl<$Res>
    implements $AttendanceStateCopyWith<$Res> {
  _$AttendanceStateCopyWithImpl(this._self, this._then);

  final AttendanceState _self;
  final $Res Function(AttendanceState) _then;

/// Create a copy of AttendanceState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? child = null,}) {
  return _then(_self.copyWith(
child: null == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as RegisteredChild,
  ));
}
/// Create a copy of AttendanceState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RegisteredChildCopyWith<$Res> get child {
  
  return $RegisteredChildCopyWith<$Res>(_self.child, (value) {
    return _then(_self.copyWith(child: value));
  });
}
}


/// @nodoc


class _AttendanceState implements AttendanceState {
  const _AttendanceState({required this.child});
  

@override final  RegisteredChild child;

/// Create a copy of AttendanceState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AttendanceStateCopyWith<_AttendanceState> get copyWith => __$AttendanceStateCopyWithImpl<_AttendanceState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AttendanceState&&(identical(other.child, child) || other.child == child));
}


@override
int get hashCode => Object.hash(runtimeType,child);

@override
String toString() {
  return 'AttendanceState(child: $child)';
}


}

/// @nodoc
abstract mixin class _$AttendanceStateCopyWith<$Res> implements $AttendanceStateCopyWith<$Res> {
  factory _$AttendanceStateCopyWith(_AttendanceState value, $Res Function(_AttendanceState) _then) = __$AttendanceStateCopyWithImpl;
@override @useResult
$Res call({
 RegisteredChild child
});


@override $RegisteredChildCopyWith<$Res> get child;

}
/// @nodoc
class __$AttendanceStateCopyWithImpl<$Res>
    implements _$AttendanceStateCopyWith<$Res> {
  __$AttendanceStateCopyWithImpl(this._self, this._then);

  final _AttendanceState _self;
  final $Res Function(_AttendanceState) _then;

/// Create a copy of AttendanceState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? child = null,}) {
  return _then(_AttendanceState(
child: null == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as RegisteredChild,
  ));
}

/// Create a copy of AttendanceState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RegisteredChildCopyWith<$Res> get child {
  
  return $RegisteredChildCopyWith<$Res>(_self.child, (value) {
    return _then(_self.copyWith(child: value));
  });
}
}

// dart format on
