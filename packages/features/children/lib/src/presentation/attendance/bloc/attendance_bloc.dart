import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/domain/usecases/save_attendance_for_child_usecase.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

part 'attendance_event.dart';
part 'attendance_state.dart';
part 'attendance_bloc.freezed.dart';

@injectable
class AttendanceBloc extends Bloc<AttendanceEvent, AttendanceState> {
  AttendanceBloc(
    this._saveAttendanceForChildUsecase,
    @factoryParam RegisteredChild child,
  ) : super(AttendanceState(child: child)) {
    on<_SetAttendanceAttendanceEvent>(_onSetAttendance);
  }

  final SaveAttendanceForChildUsecase _saveAttendanceForChildUsecase;

  FutureOr<void> _onSetAttendance(
    _SetAttendanceAttendanceEvent event,
    Emitter<AttendanceState> emit,
  ) {
    _saveAttendanceForChildUsecase(
      child: state.child,
      attendance: event.attendace,
    );
  }
}
