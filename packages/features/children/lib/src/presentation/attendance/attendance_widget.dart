import 'package:app_di/app_di.dart';
import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/presentation/attendance/bloc/attendance_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AttendanceWidget extends StatelessWidget {
  const AttendanceWidget({required this.child, super.key, this.onAttendance});

  final RegisteredChild child;
  final ValueChanged<bool>? onAttendance;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => diContainer<AttendanceBloc>(
        parameter: child,
      ),
      child: _Content(onAttendance: onAttendance),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content({this.onAttendance});

  final ValueChanged<bool>? onAttendance;

  void _setAttendance(BuildContext context, [bool attendance = true]) {
    context.read<AttendanceBloc>().add(
      AttendanceEvent.setAttendance(attendace: attendance),
    );
    onAttendance?.call(attendance);
    Navigator.maybePop(context);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: dimen.all.xs,
        child: BlocBuilder<AttendanceBloc, AttendanceState>(
          builder: (context, state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('${state.child.fullName}'),
                const SizedBox(height: xs),
                PrimaryButton.responsive(
                  title: 'Asistió',
                  onPressed: () => _setAttendance(context),
                ),
                const SizedBox(height: micro),
                TertiaryButton.responsive(
                  title: 'No Asistió',
                  onPressed: () => _setAttendance(context, false),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
