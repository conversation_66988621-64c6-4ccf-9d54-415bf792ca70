import 'package:app_di/app_di.dart';
import 'package:children/src/domain/models/school_year.dart';
import 'package:children/src/presentation/children_list/bloc/children_list_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ChildrenListPage extends StatelessWidget {
  const ChildrenListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => diContainer<ChildrenListBloc>(),
      child: const _Content(),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    final bloc = context.read<ChildrenListBloc>();
    return Scaffold(
      bottomSheet: const _BottomButtonsSheet(),

      appBar: AppBar(
        title: const Text('Listado'),
        actions: [
          PrimaryButton.small(
            onPressed: () =>
                bloc.add(const ChildrenListEvent.goToRegisterChild()),
            title: 'Añadir',
          ),
          const SizedBox(width: xs),
        ],
      ),
      body: BlocConsumer<ChildrenListBloc, ChildrenListState>(
        listenWhen: (previous, current) =>
            previous.status.isError != current.status.isError,
        listener: (context, state) {
          if (state.status.isError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                showCloseIcon: true,
                behavior: SnackBarBehavior.floating,
                backgroundColor: theme.colorScheme.error,
                content: const Text(
                  'Ha ocurrido un error. Intente nuevamente.',
                ),
              ),
            );
          }
        },
        builder: (_, state) {
          if (state.children.isEmpty) {
            return const Center(
              child: Text(
                'Aún no tiene hijos(as) registrados.\nToque añadir en la esquina superior derecha.',
              ),
            );
          }
          return ListView.builder(
            itemCount: state.children.length,
            itemBuilder: (context, index) {
              final child = state.children[index];
              final tile = ListTile(
                contentPadding: dimen.x.xs,
                leading: CircleAvatar(
                  backgroundColor: context.appTheme.semanticColorsTheme.grey,
                  child: const Icon(Icons.person),
                ),
                trailing: const Icon(LMIcons.chevronRight),
                title: Text(
                  '${child.name} ${child.lastName}',
                  style: context.appTheme.appTextTheme.body1,
                ),
                subtitle: Text(
                  'Curso: ${child.schoolYear.getName()}',
                  style: context.appTheme.appTextTheme.body3,
                ),
                onTap: () => bloc.add(
                  ChildrenListEvent.goToChildDetails(child),
                ),
              );
              return tile;
            },
          );
        },
      ),
    );
  }
}

class _BottomButtonsSheet extends StatelessWidget {
  const _BottomButtonsSheet();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: dimen.all.xs,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SecondaryButton.responsive(
              title: 'Cerrar Sesión',
              onPressed: () => context.read<ChildrenListBloc>().add(
                const ChildrenListEvent.closeSession(),
              ),
            ),
            const SizedBox(height: xs),
          ],
        ),
      ),
    );
  }
}
