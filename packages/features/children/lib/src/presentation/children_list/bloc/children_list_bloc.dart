import 'package:auth/auth.dart';
import 'package:bloc/bloc.dart';
import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/usecases/get_children_usecase.dart';
import 'package:children/src/navigator/children_navigator.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'children_list_event.dart';
part 'children_list_state.dart';
part 'children_list_bloc.freezed.dart';

@injectable
class ChildrenListBloc extends Bloc<ChildrenListEvent, ChildrenListState> {
  ChildrenListBloc(
    this._getChildrenUsecase,
    this._getCurrentUserUsecase,
    this._navigator,
    this._signOutUsecase,
  ) : super(const ChildrenListState()) {
    on<ChildrenListEvent>(
      (event, emit) => switch (event) {
        _LoadChildrenListEvent() => _onLoad(emit, event),
        _GoToRegisterChildChildrenListEvent() => _onRegisterChild(emit, event),
        _GoToChildDetailsChildrenListEvent() => _onGoToChildDetails(event),
        _CloseSessionChildrenListEvent() => _onCloseSesion(),
        _ => null,
      },
    );

    add(const ChildrenListEvent.load());
  }

  final GetChildrenUsecase _getChildrenUsecase;
  final GetCurrentUserUsecase _getCurrentUserUsecase;
  final SignOutUsecase _signOutUsecase;
  final ChildrenNavigator _navigator;

  Future<void> _onLoad(
    Emitter<ChildrenListState> emit,
    _LoadChildrenListEvent event,
  ) async {
    emit(state.copyWith(status: const ProcessState.loading()));

    final userId = _getCurrentUserUsecase().getOrNull();
    if (userId.isNotNullOrEmpty) {
      final response = await _getChildrenUsecase(userId!);

      response.fold(
        (children) => emit(
          state.copyWith(
            children: children,
            status: const ProcessState.loaded(),
          ),
        ),
        (f) => emit(state.copyWith(status: ProcessState.error(f))),
      );
    } else {
      emit(state.copyWith(status: ProcessState.error(Exception())));
    }
  }

  Future<void> _onRegisterChild(
    Emitter<ChildrenListState> emit,
    _GoToRegisterChildChildrenListEvent event,
  ) async {
    await _navigator.pushRegisterChild();

    add(const _LoadChildrenListEvent());
  }

  void _onGoToChildDetails(_GoToChildDetailsChildrenListEvent event) {
    _navigator.pushChildDetails(event.child);
  }

  Future<void> _onCloseSesion() async {
    await _signOutUsecase();
    _navigator.pushHome();
  }
}
