// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'children_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ChildrenListEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChildrenListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChildrenListEvent()';
}


}

/// @nodoc
class $ChildrenListEventCopyWith<$Res>  {
$ChildrenListEventCopyWith(ChildrenListEvent _, $Res Function(ChildrenListEvent) __);
}


/// @nodoc


class _LoadChildrenListEvent implements ChildrenListEvent {
  const _LoadChildrenListEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadChildrenListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChildrenListEvent.load()';
}


}




/// @nodoc


class _GoToRegisterChildChildrenListEvent implements ChildrenListEvent {
  const _GoToRegisterChildChildrenListEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToRegisterChildChildrenListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChildrenListEvent.goToRegisterChild()';
}


}




/// @nodoc


class _GoToChildDetailsChildrenListEvent implements ChildrenListEvent {
  const _GoToChildDetailsChildrenListEvent(this.child);
  

 final  Child child;

/// Create a copy of ChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GoToChildDetailsChildrenListEventCopyWith<_GoToChildDetailsChildrenListEvent> get copyWith => __$GoToChildDetailsChildrenListEventCopyWithImpl<_GoToChildDetailsChildrenListEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToChildDetailsChildrenListEvent&&(identical(other.child, child) || other.child == child));
}


@override
int get hashCode => Object.hash(runtimeType,child);

@override
String toString() {
  return 'ChildrenListEvent.goToChildDetails(child: $child)';
}


}

/// @nodoc
abstract mixin class _$GoToChildDetailsChildrenListEventCopyWith<$Res> implements $ChildrenListEventCopyWith<$Res> {
  factory _$GoToChildDetailsChildrenListEventCopyWith(_GoToChildDetailsChildrenListEvent value, $Res Function(_GoToChildDetailsChildrenListEvent) _then) = __$GoToChildDetailsChildrenListEventCopyWithImpl;
@useResult
$Res call({
 Child child
});


$ChildCopyWith<$Res> get child;

}
/// @nodoc
class __$GoToChildDetailsChildrenListEventCopyWithImpl<$Res>
    implements _$GoToChildDetailsChildrenListEventCopyWith<$Res> {
  __$GoToChildDetailsChildrenListEventCopyWithImpl(this._self, this._then);

  final _GoToChildDetailsChildrenListEvent _self;
  final $Res Function(_GoToChildDetailsChildrenListEvent) _then;

/// Create a copy of ChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? child = null,}) {
  return _then(_GoToChildDetailsChildrenListEvent(
null == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as Child,
  ));
}

/// Create a copy of ChildrenListEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChildCopyWith<$Res> get child {
  
  return $ChildCopyWith<$Res>(_self.child, (value) {
    return _then(_self.copyWith(child: value));
  });
}
}

/// @nodoc


class _CloseSessionChildrenListEvent implements ChildrenListEvent {
  const _CloseSessionChildrenListEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CloseSessionChildrenListEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChildrenListEvent.closeSession()';
}


}




/// @nodoc
mixin _$ChildrenListState {

 List<Child> get children; ProcessState get status;
/// Create a copy of ChildrenListState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChildrenListStateCopyWith<ChildrenListState> get copyWith => _$ChildrenListStateCopyWithImpl<ChildrenListState>(this as ChildrenListState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChildrenListState&&const DeepCollectionEquality().equals(other.children, children)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(children),status);

@override
String toString() {
  return 'ChildrenListState(children: $children, status: $status)';
}


}

/// @nodoc
abstract mixin class $ChildrenListStateCopyWith<$Res>  {
  factory $ChildrenListStateCopyWith(ChildrenListState value, $Res Function(ChildrenListState) _then) = _$ChildrenListStateCopyWithImpl;
@useResult
$Res call({
 List<Child> children, ProcessState status
});


$ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class _$ChildrenListStateCopyWithImpl<$Res>
    implements $ChildrenListStateCopyWith<$Res> {
  _$ChildrenListStateCopyWithImpl(this._self, this._then);

  final ChildrenListState _self;
  final $Res Function(ChildrenListState) _then;

/// Create a copy of ChildrenListState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? children = null,Object? status = null,}) {
  return _then(_self.copyWith(
children: null == children ? _self.children : children // ignore: cast_nullable_to_non_nullable
as List<Child>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,
  ));
}
/// Create a copy of ChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}


/// @nodoc


class _ChildrenListState implements ChildrenListState {
  const _ChildrenListState({final  List<Child> children = const [], this.status = const ProcessState.initial()}): _children = children;
  

 final  List<Child> _children;
@override@JsonKey() List<Child> get children {
  if (_children is EqualUnmodifiableListView) return _children;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_children);
}

@override@JsonKey() final  ProcessState status;

/// Create a copy of ChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChildrenListStateCopyWith<_ChildrenListState> get copyWith => __$ChildrenListStateCopyWithImpl<_ChildrenListState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChildrenListState&&const DeepCollectionEquality().equals(other._children, _children)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_children),status);

@override
String toString() {
  return 'ChildrenListState(children: $children, status: $status)';
}


}

/// @nodoc
abstract mixin class _$ChildrenListStateCopyWith<$Res> implements $ChildrenListStateCopyWith<$Res> {
  factory _$ChildrenListStateCopyWith(_ChildrenListState value, $Res Function(_ChildrenListState) _then) = __$ChildrenListStateCopyWithImpl;
@override @useResult
$Res call({
 List<Child> children, ProcessState status
});


@override $ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class __$ChildrenListStateCopyWithImpl<$Res>
    implements _$ChildrenListStateCopyWith<$Res> {
  __$ChildrenListStateCopyWithImpl(this._self, this._then);

  final _ChildrenListState _self;
  final $Res Function(_ChildrenListState) _then;

/// Create a copy of ChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? children = null,Object? status = null,}) {
  return _then(_ChildrenListState(
children: null == children ? _self._children : children // ignore: cast_nullable_to_non_nullable
as List<Child>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,
  ));
}

/// Create a copy of ChildrenListState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}

// dart format on
