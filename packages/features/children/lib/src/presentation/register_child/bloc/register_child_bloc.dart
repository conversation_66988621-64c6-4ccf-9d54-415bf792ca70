import 'package:auth/auth.dart';
import 'package:bloc/bloc.dart';
import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/models/prices.dart';
import 'package:children/src/domain/models/school_year.dart';
import 'package:children/src/domain/models/starting_date.dart';
import 'package:children/src/domain/models/week_days.dart';
import 'package:children/src/domain/usecases/save_child_usecase.dart';
import 'package:children/src/navigator/children_navigator.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'register_child_event.dart';
part 'register_child_state.dart';
part 'register_child_bloc.freezed.dart';

@injectable
class RegisterChildBloc extends Bloc<RegisterChildEvent, RegisterChildState> {
  RegisterChildBloc(
    this._saveChildUsecase,
    this._getCurrentUserUsecase,
    this._navigator,
  ) : super(RegisterChildState(prices: Prices())) {
    on<RegisterChildEvent>(
      (event, emit) => switch (event) {
        _LoadRegisterChildEvent() => _onLoad(emit, event),
        _ChangeFieldRegisterChildEvent() => _onChangeField(emit, event),
        _SaveRegisterChildEvent() => _onSaveChild(emit, event),
        _ => null,
      },
    );
  }

  final SaveChildUsecase _saveChildUsecase;
  final GetCurrentUserUsecase _getCurrentUserUsecase;
  final ChildrenNavigator _navigator;

  void _onLoad(
    Emitter<RegisterChildState> emit,
    _LoadRegisterChildEvent event,
  ) {}

  void _onChangeField(
    Emitter<RegisterChildState> emit,
    _ChangeFieldRegisterChildEvent event,
  ) {
    Set<WeekDays> _parseCheckBox(Map<WeekDays, bool>? map) =>
        map?.entries
            .where((entry) => entry.value)
            .map((el) => el.key)
            .toSet() ??
        state.child.weekAssitance;

    final child = state.child.copyWith(
      name: event.name ?? state.child.name,
      lastName: event.lastName ?? state.child.lastName,
      dateOfBirth: event.dateOfBirth ?? state.child.dateOfBirth,
      schoolYear: event.schoolYear ?? state.child.schoolYear,
      alergies: event.alergies?.split(',') ?? state.child.alergies,
      weekAssitance: _parseCheckBox(event.weekAssitance),
      isAllowedToExit: event.isAllowedToExit ?? state.child.isAllowedToExit,
      hasAttendendInPreviousCourses:
          event.hasAttendendInPreviousCourses ??
          state.child.hasAttendendInPreviousCourses,
      hasSiblingsWhoNeedsDinning:
          event.hasSiblingsWhoNeedsDinning ??
          state.child.hasSiblingsWhoNeedsDinning,
      hasDinningScholarship:
          event.hasDinningScholarship ?? state.child.hasDinningScholarship,
      isDinningIn: event.isDinningIn ?? state.child.isDinningIn,
      startingDate: event.startingDate ?? state.child.startingDate,
    );

    emit(
      state.copyWith(
        isValid: child.isValid(),
        child: child,
        longPrice:
            state.prices.longMonthPrices[child.weekAssitance.isNotEmpty
                ? child.weekAssitance.length - 1
                : 0],
        shortPrice:
            state.prices.shortMonthPrices[child.weekAssitance.isNotEmpty
                ? child.weekAssitance.length - 1
                : 0],
      ),
    );
  }

  Future<void> _onSaveChild(
    Emitter<RegisterChildState> emit,
    _SaveRegisterChildEvent event,
  ) async {
    emit(state.copyWith(status: const ProcessState.loading()));

    final userId = _getCurrentUserUsecase().getOrNull();
    if (state.isValid && userId.isNotNullOrEmpty) {
      final response = await _saveChildUsecase(
        userId!,
        state.child.toModel(),
      );

      response.fold(
        (_) => _navigator.goBackToChildrenList(),
        (f) => emit(
          state.copyWith(status: ProcessState.error(f)),
        ),
      );
    } else {
      emit(
        state.copyWith(status: ProcessState.error(Exception())),
      );
    }
  }
}
