part of 'register_child_bloc.dart';

@freezed
abstract class RegisterChildEvent with _$RegisterChildEvent {
  const factory RegisterChildEvent.load() = _LoadRegisterChildEvent;

  const factory RegisterChildEvent.changeField({
    String? name,
    String? lastName,
    DateTime? dateOfBirth,
    SchoolYear? schoolYear,
    String? alergies,
    Map<WeekDays, bool>? weekAssitance,
    String? avatarUrl,
    bool? isAllowedToExit,
    bool? hasAttendendInPreviousCourses,
    bool? hasSiblingsWhoNeedsDinning,
    bool? hasDinningScholarship,
    bool? isDinningIn,
    StartingDate? startingDate,
  }) = _ChangeFieldRegisterChildEvent;

  const factory RegisterChildEvent.saveChild() = _SaveRegisterChildEvent;
}
