// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_child_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$RegisterChildEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisterChildEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RegisterChildEvent()';
}


}

/// @nodoc
class $RegisterChildEventCopyWith<$Res>  {
$RegisterChildEventCopyWith(RegisterChildEvent _, $Res Function(RegisterChildEvent) __);
}


/// @nodoc


class _LoadRegisterChildEvent implements RegisterChildEvent {
  const _LoadRegisterChildEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadRegisterChildEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RegisterChildEvent.load()';
}


}




/// @nodoc


class _ChangeFieldRegisterChildEvent implements RegisterChildEvent {
  const _ChangeFieldRegisterChildEvent({this.name, this.lastName, this.dateOfBirth, this.schoolYear, this.alergies, final  Map<WeekDays, bool>? weekAssitance, this.avatarUrl, this.isAllowedToExit, this.hasAttendendInPreviousCourses, this.hasSiblingsWhoNeedsDinning, this.hasDinningScholarship, this.isDinningIn, this.startingDate}): _weekAssitance = weekAssitance;
  

 final  String? name;
 final  String? lastName;
 final  DateTime? dateOfBirth;
 final  SchoolYear? schoolYear;
 final  String? alergies;
 final  Map<WeekDays, bool>? _weekAssitance;
 Map<WeekDays, bool>? get weekAssitance {
  final value = _weekAssitance;
  if (value == null) return null;
  if (_weekAssitance is EqualUnmodifiableMapView) return _weekAssitance;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

 final  String? avatarUrl;
 final  bool? isAllowedToExit;
 final  bool? hasAttendendInPreviousCourses;
 final  bool? hasSiblingsWhoNeedsDinning;
 final  bool? hasDinningScholarship;
 final  bool? isDinningIn;
 final  StartingDate? startingDate;

/// Create a copy of RegisterChildEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeFieldRegisterChildEventCopyWith<_ChangeFieldRegisterChildEvent> get copyWith => __$ChangeFieldRegisterChildEventCopyWithImpl<_ChangeFieldRegisterChildEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeFieldRegisterChildEvent&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.schoolYear, schoolYear) || other.schoolYear == schoolYear)&&(identical(other.alergies, alergies) || other.alergies == alergies)&&const DeepCollectionEquality().equals(other._weekAssitance, _weekAssitance)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isAllowedToExit, isAllowedToExit) || other.isAllowedToExit == isAllowedToExit)&&(identical(other.hasAttendendInPreviousCourses, hasAttendendInPreviousCourses) || other.hasAttendendInPreviousCourses == hasAttendendInPreviousCourses)&&(identical(other.hasSiblingsWhoNeedsDinning, hasSiblingsWhoNeedsDinning) || other.hasSiblingsWhoNeedsDinning == hasSiblingsWhoNeedsDinning)&&(identical(other.hasDinningScholarship, hasDinningScholarship) || other.hasDinningScholarship == hasDinningScholarship)&&(identical(other.isDinningIn, isDinningIn) || other.isDinningIn == isDinningIn)&&(identical(other.startingDate, startingDate) || other.startingDate == startingDate));
}


@override
int get hashCode => Object.hash(runtimeType,name,lastName,dateOfBirth,schoolYear,alergies,const DeepCollectionEquality().hash(_weekAssitance),avatarUrl,isAllowedToExit,hasAttendendInPreviousCourses,hasSiblingsWhoNeedsDinning,hasDinningScholarship,isDinningIn,startingDate);

@override
String toString() {
  return 'RegisterChildEvent.changeField(name: $name, lastName: $lastName, dateOfBirth: $dateOfBirth, schoolYear: $schoolYear, alergies: $alergies, weekAssitance: $weekAssitance, avatarUrl: $avatarUrl, isAllowedToExit: $isAllowedToExit, hasAttendendInPreviousCourses: $hasAttendendInPreviousCourses, hasSiblingsWhoNeedsDinning: $hasSiblingsWhoNeedsDinning, hasDinningScholarship: $hasDinningScholarship, isDinningIn: $isDinningIn, startingDate: $startingDate)';
}


}

/// @nodoc
abstract mixin class _$ChangeFieldRegisterChildEventCopyWith<$Res> implements $RegisterChildEventCopyWith<$Res> {
  factory _$ChangeFieldRegisterChildEventCopyWith(_ChangeFieldRegisterChildEvent value, $Res Function(_ChangeFieldRegisterChildEvent) _then) = __$ChangeFieldRegisterChildEventCopyWithImpl;
@useResult
$Res call({
 String? name, String? lastName, DateTime? dateOfBirth, SchoolYear? schoolYear, String? alergies, Map<WeekDays, bool>? weekAssitance, String? avatarUrl, bool? isAllowedToExit, bool? hasAttendendInPreviousCourses, bool? hasSiblingsWhoNeedsDinning, bool? hasDinningScholarship, bool? isDinningIn, StartingDate? startingDate
});




}
/// @nodoc
class __$ChangeFieldRegisterChildEventCopyWithImpl<$Res>
    implements _$ChangeFieldRegisterChildEventCopyWith<$Res> {
  __$ChangeFieldRegisterChildEventCopyWithImpl(this._self, this._then);

  final _ChangeFieldRegisterChildEvent _self;
  final $Res Function(_ChangeFieldRegisterChildEvent) _then;

/// Create a copy of RegisterChildEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? lastName = freezed,Object? dateOfBirth = freezed,Object? schoolYear = freezed,Object? alergies = freezed,Object? weekAssitance = freezed,Object? avatarUrl = freezed,Object? isAllowedToExit = freezed,Object? hasAttendendInPreviousCourses = freezed,Object? hasSiblingsWhoNeedsDinning = freezed,Object? hasDinningScholarship = freezed,Object? isDinningIn = freezed,Object? startingDate = freezed,}) {
  return _then(_ChangeFieldRegisterChildEvent(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,schoolYear: freezed == schoolYear ? _self.schoolYear : schoolYear // ignore: cast_nullable_to_non_nullable
as SchoolYear?,alergies: freezed == alergies ? _self.alergies : alergies // ignore: cast_nullable_to_non_nullable
as String?,weekAssitance: freezed == weekAssitance ? _self._weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Map<WeekDays, bool>?,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,isAllowedToExit: freezed == isAllowedToExit ? _self.isAllowedToExit : isAllowedToExit // ignore: cast_nullable_to_non_nullable
as bool?,hasAttendendInPreviousCourses: freezed == hasAttendendInPreviousCourses ? _self.hasAttendendInPreviousCourses : hasAttendendInPreviousCourses // ignore: cast_nullable_to_non_nullable
as bool?,hasSiblingsWhoNeedsDinning: freezed == hasSiblingsWhoNeedsDinning ? _self.hasSiblingsWhoNeedsDinning : hasSiblingsWhoNeedsDinning // ignore: cast_nullable_to_non_nullable
as bool?,hasDinningScholarship: freezed == hasDinningScholarship ? _self.hasDinningScholarship : hasDinningScholarship // ignore: cast_nullable_to_non_nullable
as bool?,isDinningIn: freezed == isDinningIn ? _self.isDinningIn : isDinningIn // ignore: cast_nullable_to_non_nullable
as bool?,startingDate: freezed == startingDate ? _self.startingDate : startingDate // ignore: cast_nullable_to_non_nullable
as StartingDate?,
  ));
}


}

/// @nodoc


class _SaveRegisterChildEvent implements RegisterChildEvent {
  const _SaveRegisterChildEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SaveRegisterChildEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'RegisterChildEvent.saveChild()';
}


}




/// @nodoc
mixin _$RegisterChildState {

 Prices get prices; TempChild get child; ProcessState get status; bool get isValid; int get shortPrice; int get longPrice;
/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RegisterChildStateCopyWith<RegisterChildState> get copyWith => _$RegisterChildStateCopyWithImpl<RegisterChildState>(this as RegisterChildState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisterChildState&&(identical(other.prices, prices) || other.prices == prices)&&(identical(other.child, child) || other.child == child)&&(identical(other.status, status) || other.status == status)&&(identical(other.isValid, isValid) || other.isValid == isValid)&&(identical(other.shortPrice, shortPrice) || other.shortPrice == shortPrice)&&(identical(other.longPrice, longPrice) || other.longPrice == longPrice));
}


@override
int get hashCode => Object.hash(runtimeType,prices,child,status,isValid,shortPrice,longPrice);

@override
String toString() {
  return 'RegisterChildState(prices: $prices, child: $child, status: $status, isValid: $isValid, shortPrice: $shortPrice, longPrice: $longPrice)';
}


}

/// @nodoc
abstract mixin class $RegisterChildStateCopyWith<$Res>  {
  factory $RegisterChildStateCopyWith(RegisterChildState value, $Res Function(RegisterChildState) _then) = _$RegisterChildStateCopyWithImpl;
@useResult
$Res call({
 Prices prices, TempChild child, ProcessState status, bool isValid, int shortPrice, int longPrice
});


$PricesCopyWith<$Res> get prices;$TempChildCopyWith<$Res> get child;$ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class _$RegisterChildStateCopyWithImpl<$Res>
    implements $RegisterChildStateCopyWith<$Res> {
  _$RegisterChildStateCopyWithImpl(this._self, this._then);

  final RegisterChildState _self;
  final $Res Function(RegisterChildState) _then;

/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? prices = null,Object? child = null,Object? status = null,Object? isValid = null,Object? shortPrice = null,Object? longPrice = null,}) {
  return _then(_self.copyWith(
prices: null == prices ? _self.prices : prices // ignore: cast_nullable_to_non_nullable
as Prices,child: null == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as TempChild,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,isValid: null == isValid ? _self.isValid : isValid // ignore: cast_nullable_to_non_nullable
as bool,shortPrice: null == shortPrice ? _self.shortPrice : shortPrice // ignore: cast_nullable_to_non_nullable
as int,longPrice: null == longPrice ? _self.longPrice : longPrice // ignore: cast_nullable_to_non_nullable
as int,
  ));
}
/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PricesCopyWith<$Res> get prices {
  
  return $PricesCopyWith<$Res>(_self.prices, (value) {
    return _then(_self.copyWith(prices: value));
  });
}/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TempChildCopyWith<$Res> get child {
  
  return $TempChildCopyWith<$Res>(_self.child, (value) {
    return _then(_self.copyWith(child: value));
  });
}/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}


/// @nodoc


class _RegisterChildState implements RegisterChildState {
  const _RegisterChildState({required this.prices, this.child = const TempChild(), this.status = const ProcessState.initial(), this.isValid = false, this.shortPrice = 0, this.longPrice = 0});
  

@override final  Prices prices;
@override@JsonKey() final  TempChild child;
@override@JsonKey() final  ProcessState status;
@override@JsonKey() final  bool isValid;
@override@JsonKey() final  int shortPrice;
@override@JsonKey() final  int longPrice;

/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RegisterChildStateCopyWith<_RegisterChildState> get copyWith => __$RegisterChildStateCopyWithImpl<_RegisterChildState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RegisterChildState&&(identical(other.prices, prices) || other.prices == prices)&&(identical(other.child, child) || other.child == child)&&(identical(other.status, status) || other.status == status)&&(identical(other.isValid, isValid) || other.isValid == isValid)&&(identical(other.shortPrice, shortPrice) || other.shortPrice == shortPrice)&&(identical(other.longPrice, longPrice) || other.longPrice == longPrice));
}


@override
int get hashCode => Object.hash(runtimeType,prices,child,status,isValid,shortPrice,longPrice);

@override
String toString() {
  return 'RegisterChildState(prices: $prices, child: $child, status: $status, isValid: $isValid, shortPrice: $shortPrice, longPrice: $longPrice)';
}


}

/// @nodoc
abstract mixin class _$RegisterChildStateCopyWith<$Res> implements $RegisterChildStateCopyWith<$Res> {
  factory _$RegisterChildStateCopyWith(_RegisterChildState value, $Res Function(_RegisterChildState) _then) = __$RegisterChildStateCopyWithImpl;
@override @useResult
$Res call({
 Prices prices, TempChild child, ProcessState status, bool isValid, int shortPrice, int longPrice
});


@override $PricesCopyWith<$Res> get prices;@override $TempChildCopyWith<$Res> get child;@override $ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class __$RegisterChildStateCopyWithImpl<$Res>
    implements _$RegisterChildStateCopyWith<$Res> {
  __$RegisterChildStateCopyWithImpl(this._self, this._then);

  final _RegisterChildState _self;
  final $Res Function(_RegisterChildState) _then;

/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? prices = null,Object? child = null,Object? status = null,Object? isValid = null,Object? shortPrice = null,Object? longPrice = null,}) {
  return _then(_RegisterChildState(
prices: null == prices ? _self.prices : prices // ignore: cast_nullable_to_non_nullable
as Prices,child: null == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as TempChild,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,isValid: null == isValid ? _self.isValid : isValid // ignore: cast_nullable_to_non_nullable
as bool,shortPrice: null == shortPrice ? _self.shortPrice : shortPrice // ignore: cast_nullable_to_non_nullable
as int,longPrice: null == longPrice ? _self.longPrice : longPrice // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PricesCopyWith<$Res> get prices {
  
  return $PricesCopyWith<$Res>(_self.prices, (value) {
    return _then(_self.copyWith(prices: value));
  });
}/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TempChildCopyWith<$Res> get child {
  
  return $TempChildCopyWith<$Res>(_self.child, (value) {
    return _then(_self.copyWith(child: value));
  });
}/// Create a copy of RegisterChildState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}

/// @nodoc
mixin _$TempChild {

 String? get name; String? get lastName; DateTime? get dateOfBirth; SchoolYear? get schoolYear; List<String> get alergies; Set<WeekDays> get weekAssitance; String? get avatarUrl; bool get isAllowedToExit; bool get hasAttendendInPreviousCourses; bool get hasSiblingsWhoNeedsDinning; bool get hasDinningScholarship; bool get isDinningIn; StartingDate? get startingDate;
/// Create a copy of TempChild
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TempChildCopyWith<TempChild> get copyWith => _$TempChildCopyWithImpl<TempChild>(this as TempChild, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TempChild&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.schoolYear, schoolYear) || other.schoolYear == schoolYear)&&const DeepCollectionEquality().equals(other.alergies, alergies)&&const DeepCollectionEquality().equals(other.weekAssitance, weekAssitance)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isAllowedToExit, isAllowedToExit) || other.isAllowedToExit == isAllowedToExit)&&(identical(other.hasAttendendInPreviousCourses, hasAttendendInPreviousCourses) || other.hasAttendendInPreviousCourses == hasAttendendInPreviousCourses)&&(identical(other.hasSiblingsWhoNeedsDinning, hasSiblingsWhoNeedsDinning) || other.hasSiblingsWhoNeedsDinning == hasSiblingsWhoNeedsDinning)&&(identical(other.hasDinningScholarship, hasDinningScholarship) || other.hasDinningScholarship == hasDinningScholarship)&&(identical(other.isDinningIn, isDinningIn) || other.isDinningIn == isDinningIn)&&(identical(other.startingDate, startingDate) || other.startingDate == startingDate));
}


@override
int get hashCode => Object.hash(runtimeType,name,lastName,dateOfBirth,schoolYear,const DeepCollectionEquality().hash(alergies),const DeepCollectionEquality().hash(weekAssitance),avatarUrl,isAllowedToExit,hasAttendendInPreviousCourses,hasSiblingsWhoNeedsDinning,hasDinningScholarship,isDinningIn,startingDate);

@override
String toString() {
  return 'TempChild(name: $name, lastName: $lastName, dateOfBirth: $dateOfBirth, schoolYear: $schoolYear, alergies: $alergies, weekAssitance: $weekAssitance, avatarUrl: $avatarUrl, isAllowedToExit: $isAllowedToExit, hasAttendendInPreviousCourses: $hasAttendendInPreviousCourses, hasSiblingsWhoNeedsDinning: $hasSiblingsWhoNeedsDinning, hasDinningScholarship: $hasDinningScholarship, isDinningIn: $isDinningIn, startingDate: $startingDate)';
}


}

/// @nodoc
abstract mixin class $TempChildCopyWith<$Res>  {
  factory $TempChildCopyWith(TempChild value, $Res Function(TempChild) _then) = _$TempChildCopyWithImpl;
@useResult
$Res call({
 String? name, String? lastName, DateTime? dateOfBirth, SchoolYear? schoolYear, List<String> alergies, Set<WeekDays> weekAssitance, String? avatarUrl, bool isAllowedToExit, bool hasAttendendInPreviousCourses, bool hasSiblingsWhoNeedsDinning, bool hasDinningScholarship, bool isDinningIn, StartingDate? startingDate
});




}
/// @nodoc
class _$TempChildCopyWithImpl<$Res>
    implements $TempChildCopyWith<$Res> {
  _$TempChildCopyWithImpl(this._self, this._then);

  final TempChild _self;
  final $Res Function(TempChild) _then;

/// Create a copy of TempChild
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = freezed,Object? lastName = freezed,Object? dateOfBirth = freezed,Object? schoolYear = freezed,Object? alergies = null,Object? weekAssitance = null,Object? avatarUrl = freezed,Object? isAllowedToExit = null,Object? hasAttendendInPreviousCourses = null,Object? hasSiblingsWhoNeedsDinning = null,Object? hasDinningScholarship = null,Object? isDinningIn = null,Object? startingDate = freezed,}) {
  return _then(_self.copyWith(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,schoolYear: freezed == schoolYear ? _self.schoolYear : schoolYear // ignore: cast_nullable_to_non_nullable
as SchoolYear?,alergies: null == alergies ? _self.alergies : alergies // ignore: cast_nullable_to_non_nullable
as List<String>,weekAssitance: null == weekAssitance ? _self.weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Set<WeekDays>,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,isAllowedToExit: null == isAllowedToExit ? _self.isAllowedToExit : isAllowedToExit // ignore: cast_nullable_to_non_nullable
as bool,hasAttendendInPreviousCourses: null == hasAttendendInPreviousCourses ? _self.hasAttendendInPreviousCourses : hasAttendendInPreviousCourses // ignore: cast_nullable_to_non_nullable
as bool,hasSiblingsWhoNeedsDinning: null == hasSiblingsWhoNeedsDinning ? _self.hasSiblingsWhoNeedsDinning : hasSiblingsWhoNeedsDinning // ignore: cast_nullable_to_non_nullable
as bool,hasDinningScholarship: null == hasDinningScholarship ? _self.hasDinningScholarship : hasDinningScholarship // ignore: cast_nullable_to_non_nullable
as bool,isDinningIn: null == isDinningIn ? _self.isDinningIn : isDinningIn // ignore: cast_nullable_to_non_nullable
as bool,startingDate: freezed == startingDate ? _self.startingDate : startingDate // ignore: cast_nullable_to_non_nullable
as StartingDate?,
  ));
}

}


/// @nodoc


class _TempChild implements TempChild {
  const _TempChild({this.name, this.lastName, this.dateOfBirth, this.schoolYear, final  List<String> alergies = const [], final  Set<WeekDays> weekAssitance = const {}, this.avatarUrl, this.isAllowedToExit = false, this.hasAttendendInPreviousCourses = false, this.hasSiblingsWhoNeedsDinning = false, this.hasDinningScholarship = false, this.isDinningIn = true, this.startingDate}): _alergies = alergies,_weekAssitance = weekAssitance;
  

@override final  String? name;
@override final  String? lastName;
@override final  DateTime? dateOfBirth;
@override final  SchoolYear? schoolYear;
 final  List<String> _alergies;
@override@JsonKey() List<String> get alergies {
  if (_alergies is EqualUnmodifiableListView) return _alergies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_alergies);
}

 final  Set<WeekDays> _weekAssitance;
@override@JsonKey() Set<WeekDays> get weekAssitance {
  if (_weekAssitance is EqualUnmodifiableSetView) return _weekAssitance;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableSetView(_weekAssitance);
}

@override final  String? avatarUrl;
@override@JsonKey() final  bool isAllowedToExit;
@override@JsonKey() final  bool hasAttendendInPreviousCourses;
@override@JsonKey() final  bool hasSiblingsWhoNeedsDinning;
@override@JsonKey() final  bool hasDinningScholarship;
@override@JsonKey() final  bool isDinningIn;
@override final  StartingDate? startingDate;

/// Create a copy of TempChild
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TempChildCopyWith<_TempChild> get copyWith => __$TempChildCopyWithImpl<_TempChild>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TempChild&&(identical(other.name, name) || other.name == name)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth)&&(identical(other.schoolYear, schoolYear) || other.schoolYear == schoolYear)&&const DeepCollectionEquality().equals(other._alergies, _alergies)&&const DeepCollectionEquality().equals(other._weekAssitance, _weekAssitance)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.isAllowedToExit, isAllowedToExit) || other.isAllowedToExit == isAllowedToExit)&&(identical(other.hasAttendendInPreviousCourses, hasAttendendInPreviousCourses) || other.hasAttendendInPreviousCourses == hasAttendendInPreviousCourses)&&(identical(other.hasSiblingsWhoNeedsDinning, hasSiblingsWhoNeedsDinning) || other.hasSiblingsWhoNeedsDinning == hasSiblingsWhoNeedsDinning)&&(identical(other.hasDinningScholarship, hasDinningScholarship) || other.hasDinningScholarship == hasDinningScholarship)&&(identical(other.isDinningIn, isDinningIn) || other.isDinningIn == isDinningIn)&&(identical(other.startingDate, startingDate) || other.startingDate == startingDate));
}


@override
int get hashCode => Object.hash(runtimeType,name,lastName,dateOfBirth,schoolYear,const DeepCollectionEquality().hash(_alergies),const DeepCollectionEquality().hash(_weekAssitance),avatarUrl,isAllowedToExit,hasAttendendInPreviousCourses,hasSiblingsWhoNeedsDinning,hasDinningScholarship,isDinningIn,startingDate);

@override
String toString() {
  return 'TempChild(name: $name, lastName: $lastName, dateOfBirth: $dateOfBirth, schoolYear: $schoolYear, alergies: $alergies, weekAssitance: $weekAssitance, avatarUrl: $avatarUrl, isAllowedToExit: $isAllowedToExit, hasAttendendInPreviousCourses: $hasAttendendInPreviousCourses, hasSiblingsWhoNeedsDinning: $hasSiblingsWhoNeedsDinning, hasDinningScholarship: $hasDinningScholarship, isDinningIn: $isDinningIn, startingDate: $startingDate)';
}


}

/// @nodoc
abstract mixin class _$TempChildCopyWith<$Res> implements $TempChildCopyWith<$Res> {
  factory _$TempChildCopyWith(_TempChild value, $Res Function(_TempChild) _then) = __$TempChildCopyWithImpl;
@override @useResult
$Res call({
 String? name, String? lastName, DateTime? dateOfBirth, SchoolYear? schoolYear, List<String> alergies, Set<WeekDays> weekAssitance, String? avatarUrl, bool isAllowedToExit, bool hasAttendendInPreviousCourses, bool hasSiblingsWhoNeedsDinning, bool hasDinningScholarship, bool isDinningIn, StartingDate? startingDate
});




}
/// @nodoc
class __$TempChildCopyWithImpl<$Res>
    implements _$TempChildCopyWith<$Res> {
  __$TempChildCopyWithImpl(this._self, this._then);

  final _TempChild _self;
  final $Res Function(_TempChild) _then;

/// Create a copy of TempChild
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = freezed,Object? lastName = freezed,Object? dateOfBirth = freezed,Object? schoolYear = freezed,Object? alergies = null,Object? weekAssitance = null,Object? avatarUrl = freezed,Object? isAllowedToExit = null,Object? hasAttendendInPreviousCourses = null,Object? hasSiblingsWhoNeedsDinning = null,Object? hasDinningScholarship = null,Object? isDinningIn = null,Object? startingDate = freezed,}) {
  return _then(_TempChild(
name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,lastName: freezed == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String?,dateOfBirth: freezed == dateOfBirth ? _self.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,schoolYear: freezed == schoolYear ? _self.schoolYear : schoolYear // ignore: cast_nullable_to_non_nullable
as SchoolYear?,alergies: null == alergies ? _self._alergies : alergies // ignore: cast_nullable_to_non_nullable
as List<String>,weekAssitance: null == weekAssitance ? _self._weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Set<WeekDays>,avatarUrl: freezed == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String?,isAllowedToExit: null == isAllowedToExit ? _self.isAllowedToExit : isAllowedToExit // ignore: cast_nullable_to_non_nullable
as bool,hasAttendendInPreviousCourses: null == hasAttendendInPreviousCourses ? _self.hasAttendendInPreviousCourses : hasAttendendInPreviousCourses // ignore: cast_nullable_to_non_nullable
as bool,hasSiblingsWhoNeedsDinning: null == hasSiblingsWhoNeedsDinning ? _self.hasSiblingsWhoNeedsDinning : hasSiblingsWhoNeedsDinning // ignore: cast_nullable_to_non_nullable
as bool,hasDinningScholarship: null == hasDinningScholarship ? _self.hasDinningScholarship : hasDinningScholarship // ignore: cast_nullable_to_non_nullable
as bool,isDinningIn: null == isDinningIn ? _self.isDinningIn : isDinningIn // ignore: cast_nullable_to_non_nullable
as bool,startingDate: freezed == startingDate ? _self.startingDate : startingDate // ignore: cast_nullable_to_non_nullable
as StartingDate?,
  ));
}


}

// dart format on
