part of 'register_child_bloc.dart';

@freezed
abstract class RegisterChildState with _$RegisterChildState {
  const factory RegisterChildState({
    required Prices prices,
    @Default(TempChild()) TempChild child,
    @Default(ProcessState.initial()) ProcessState status,
    @Default(false) bool isValid,
    @Default(0) int shortPrice,
    @Default(0) int longPrice,
  }) = _RegisterChildState;
}

@freezed
abstract class TempChild with _$TempChild {
  const factory TempChild({
    String? name,
    String? lastName,
    DateTime? dateOfBirth,
    SchoolYear? schoolYear,
    @Default([]) List<String> alergies,
    @Default({}) Set<WeekDays> weekAssitance,
    String? avatarUrl,
    @Default(false) bool isAllowedToExit,
    @Default(false) bool hasAttendendInPreviousCourses,
    @Default(false) bool hasSiblingsWhoNeedsDinning,
    @Default(false) bool hasDinningScholarship,
    @Default(true) bool isDinningIn,
    StartingDate? startingDate,
  }) = _TempChild;
}

extension on TempChild {
  bool isValid() =>
      name.isNotNullOrEmpty &&
      lastName.isNotNullOrEmpty &&
      dateOfBirth.isNotNull &&
      schoolYear.isNotNull &&
      startingDate.isNotNull &&
      weekAssitance.length > 0;

  Child toModel() => Child(
    name: name!,
    lastName: lastName!,
    dateOfBirth: dateOfBirth!,
    schoolYear: schoolYear!,
    alergies: alergies,
    weekAssitance: weekAssitance,
    avatarUrl: '',
    hasAttendendInPreviousCourses: hasAttendendInPreviousCourses,
    hasSiblingsWhoNeedsDinning: hasSiblingsWhoNeedsDinning,
    hasDinningScholarship: hasDinningScholarship,
    isDinningIn: hasDinningScholarship ? true : isDinningIn,
    startingDate: startingDate!,
    isAllowedToExit: isAllowedToExit,
  );
}
