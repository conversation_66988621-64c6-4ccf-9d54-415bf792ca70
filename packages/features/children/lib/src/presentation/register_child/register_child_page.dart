import 'package:children/src/domain/models/school_year.dart';
import 'package:children/src/domain/models/starting_date.dart';
import 'package:children/src/domain/models/week_days.dart';
import 'package:children/src/presentation/register_child/bloc/register_child_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:prelude/prelude.dart';

class RegisterChildPage extends StatelessWidget {
  const RegisterChildPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance.get<RegisterChildBloc>(),
      child: Scaffold(
        appBar: AppBar(),
        bottomSheet: const _BottomButtonsSheet(),
        body: GestureDetector(
          onTap: kIsWeb
              ? null
              : () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Safe<PERSON>rea(
            child: Padding(
              padding: dimen.all.xs,
              child: _Content(),
            ),
          ),
        ),
      ),
    );
  }
}

class _Content extends StatelessWidget {
  _Content();

  String? _mandatoryFieldValidation(String? value) =>
      value.isNotNullOrEmpty ? null : 'Este campo es obligatorio';

  final _dateController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    final bloc = context.read<RegisterChildBloc>();
    final schoolYears = SchoolYear.values.where(
      (el) => el != SchoolYear.unknown,
    );
    final startingDate = StartingDate.values.where(
      (el) => el != StartingDate.unknown,
    );
    return BlocConsumer<RegisterChildBloc, RegisterChildState>(
      listenWhen: (previous, current) =>
          previous.status.isError != current.status.isError,
      listener: (context, state) {
        if (state.status.isError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              showCloseIcon: true,
              behavior: SnackBarBehavior.floating,
              backgroundColor: theme.colorScheme.error,
              content: const Text('Ha ocurrido un error. Intente nuevamente.'),
            ),
          );
        }
      },
      builder: (_, state) => Form(
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Registar hijo(a)',
                    style: theme.appTextTheme.headline2,
                  ),
                ],
              ),
              const SizedBox(height: md),
              Text(
                'Nombre(s)',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                enabled: !state.status.isLoading,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                initialValue: state.child.name,
                validator: _mandatoryFieldValidation,
                onChanged: (text) => bloc.add(
                  RegisterChildEvent.changeField(name: text),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Apellidos',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                textInputAction: TextInputAction.next,
                initialValue: state.child.lastName,
                validator: _mandatoryFieldValidation,
                onChanged: (text) => bloc.add(
                  RegisterChildEvent.changeField(lastName: text),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Fecha de nacimiento',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                textInputAction: TextInputAction.next,
                validator: _mandatoryFieldValidation,
                controller: _dateController
                  ..text = state.child.dateOfBirth?.toLocalString() ?? '',
                readOnly: true,
                decoration: InputDecoration(
                  suffixIcon: IconButton(
                    onPressed: () => _showDatePicker(context, bloc),
                    icon: const Icon(Icons.calendar_today_rounded),
                  ),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Curso',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              ...schoolYears.map(
                (el) => RadioListTile(
                  title: Text(el.getName()),
                  value: el,
                  groupValue: state.child.schoolYear,
                  onChanged: (value) {
                    bloc.add(
                      RegisterChildEvent.changeField(schoolYear: value),
                    );
                  },
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Alergias / Intolerancias (Opcional)',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              TextFormField(
                textInputAction: TextInputAction.next,
                decoration: InputDecoration(
                  hintText: 'lácteos, soja, gluten',
                  hintStyle: TextStyle(color: theme.semanticColorsTheme.grey),
                  helperText:
                      'Alergias y/o intolerancias separadas por una coma (,)',
                ),
                onChanged: (text) => bloc.add(
                  RegisterChildEvent.changeField(alergies: text),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Asistencia',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              WeekdaysCheckboxGroup(
                onDayChanged: (value) => context.read<RegisterChildBloc>().add(
                  RegisterChildEvent.changeField(weekAssitance: value),
                ),
              ),
              const SizedBox(height: xs),
              Text(
                'Fecha de inicio',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xxs),
              ...startingDate.map(
                (el) => RadioListTile(
                  title: Text(el.getName()),
                  value: el,
                  groupValue: state.child.startingDate,
                  onChanged: (value) {
                    bloc.add(
                      RegisterChildEvent.changeField(startingDate: value),
                    );
                  },
                ),
              ),
              const SizedBox(height: xs),
              if (state.child.weekAssitance.isNotEmpty)
                PriceInfo(
                  shortPrice: state.shortPrice,
                  longPrice: state.longPrice,
                ),
              const SizedBox(height: xs),
              Text(
                'Permiso de salida (15:00h)',
                style: theme.appTextTheme.headline5,
              ),
              CheckboxListTile(
                title: const Text(
                  'Acepto que mi hijo(a) abandone el centro a las 15:00h sin supervisión de un adulto.',
                ),
                value: state.child.isAllowedToExit,
                onChanged: (bool? value) {
                  bloc.add(
                    RegisterChildEvent.changeField(
                      isAllowedToExit: value ?? false,
                    ),
                  );
                },
              ),
              const SizedBox(height: xs),
              Text(
                'Otros datos',
                style: theme.appTextTheme.headline5,
              ),
              const SizedBox(height: xs),
              CheckboxListTile(
                title: const Text(
                  'Mi hijo asistió al comedor en cursos anteriores.',
                ),
                value: state.child.hasAttendendInPreviousCourses,
                onChanged: (bool? value) {
                  bloc.add(
                    RegisterChildEvent.changeField(
                      hasAttendendInPreviousCourses: value ?? false,
                    ),
                  );
                },
              ),
              const SizedBox(height: xs),
              CheckboxListTile(
                title: const Text(
                  'Mi hijo tiene uno o más hermanos(as) que necesitan el servicio de comedor.',
                ),
                value: state.child.hasSiblingsWhoNeedsDinning,
                onChanged: (bool? value) {
                  bloc.add(
                    RegisterChildEvent.changeField(
                      hasSiblingsWhoNeedsDinning: value ?? false,
                    ),
                  );
                },
              ),
              const SizedBox(height: xs),
              CheckboxListTile(
                title: const Text(
                  'Mi hijo es beneficiario(a) de beca comedor. (Servicio obligatorio en el centro)',
                ),
                value: state.child.hasDinningScholarship,
                onChanged: (bool? value) {
                  bloc.add(
                    RegisterChildEvent.changeField(
                      hasDinningScholarship: value ?? false,
                    ),
                  );
                },
              ),
              const SizedBox(height: xs),
              if (!state.child.hasDinningScholarship)
                CheckboxListTile(
                  title: const Text(
                    'Mi hijo comerá en el centro. (Si no la marca se asume que será para llevar)',
                  ),
                  value: state.child.isDinningIn,
                  onChanged: (bool? value) {
                    bloc.add(
                      RegisterChildEvent.changeField(
                        isDinningIn: value ?? false,
                      ),
                    );
                  },
                ),
              const SizedBox(height: xxxl * 2),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showDatePicker(
    BuildContext context,
    RegisterChildBloc bloc,
  ) async {
    final date = await showDatePicker(
      context: context,
      firstDate: DateTime(1990),
      lastDate: DateTime.now(),
      initialEntryMode: DatePickerEntryMode.calendarOnly,
    );
    bloc.add(RegisterChildEvent.changeField(dateOfBirth: date));
  }
}

class _BottomButtonsSheet extends StatelessWidget {
  const _BottomButtonsSheet();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RegisterChildBloc, RegisterChildState>(
      builder: (context, state) {
        return SafeArea(
          child: Padding(
            padding: dimen.all.xs,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                PrimaryButton.responsive(
                  title: 'Guardar',
                  isLoading: state.status.isLoading,
                  onPressed: state.isValid
                      ? () => context.read<RegisterChildBloc>().add(
                          const RegisterChildEvent.saveChild(),
                        )
                      : null,
                ),
                const SizedBox(height: xs),
              ],
            ),
          ),
        );
      },
    );
  }
}

class WeekdaysCheckboxGroup extends StatefulWidget {
  const WeekdaysCheckboxGroup({
    super.key,
    this.onDayChanged,
    this.weekDays,
  });

  final ValueChanged<Map<WeekDays, bool>>? onDayChanged;
  final Set<WeekDays>? weekDays;

  @override
  _WeekdaysCheckboxGroupState createState() => _WeekdaysCheckboxGroupState();
}

class _WeekdaysCheckboxGroupState extends State<WeekdaysCheckboxGroup> {
  late final Map<WeekDays, bool> checkedDays;

  @override
  void initState() {
    checkedDays = {
      WeekDays.monday: widget.weekDays?.contains(WeekDays.monday) ?? false,
      WeekDays.tuesday: widget.weekDays?.contains(WeekDays.tuesday) ?? false,
      WeekDays.wednesday:
          widget.weekDays?.contains(WeekDays.wednesday) ?? false,
      WeekDays.thursday: widget.weekDays?.contains(WeekDays.thursday) ?? false,
      WeekDays.friday: widget.weekDays?.contains(WeekDays.friday) ?? false,
    };
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: checkedDays.keys.map((day) {
        return CheckboxListTile(
          title: Text(day.toLocalString()),
          value: checkedDays[day],
          onChanged: (bool? value) {
            setState(() {
              checkedDays[day] = value!;
              widget.onDayChanged?.call(checkedDays);
            });
          },
        );
      }).toList(),
    );
  }
}

class PriceInfo extends StatelessWidget {
  const PriceInfo({
    required this.shortPrice,
    required this.longPrice,
    super.key,
  });

  final int shortPrice;
  final int longPrice;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return Container(
      padding: dimen.all.xs,
      decoration: BoxDecoration(
        color: theme.semanticColorsTheme.lightYellow,
        border: Border.all(
          color: theme.semanticColorsTheme.yellow,
        ),
        borderRadius: BorderRadius.circular(xs),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Precio final',
            style: theme.appTextTheme.headline4,
          ),
          const SizedBox(height: xs),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Septiembre y Junio:',
                style: theme.appTextTheme.body1,
              ),
              Text(
                '${shortPrice}€',
                style: theme.appTextTheme.body1,
              ),
            ],
          ),
          const SizedBox(height: xxs),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Octubre a Mayo:',
                style: theme.appTextTheme.body1,
              ),
              Text(
                '${longPrice}€',
                style: theme.appTextTheme.body1,
              ),
            ],
          ),
          const SizedBox(height: xs),
          Text(
            '* Días sueltos: 6.50€ (preaviso de 48h)',
            style: theme.appTextTheme.body3,
          ),
          Text(
            '* No socios: Coste de 5€ más cada mes sobre las cuotas indicadas.',
            style: theme.appTextTheme.body3,
          ),
        ],
      ),
    );
  }
}
