import 'package:app_di/app_di.dart';
import 'package:children/src/presentation/all_children_report/bloc/all_children_report_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class AllChildrenReport extends StatelessWidget {
  const AllChildrenReport({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => diContainer<AllChildrenReportBloc>(),
      child: const _Content(),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          IconButton(
            onPressed: () => context.read<AllChildrenReportBloc>().add(
              const AllChildrenReportEvent.exportCSV(),
            ),
            icon: const Icon(Icons.file_download),
          ),
          IconButton(
            onPressed: () => context.read<AllChildrenReportBloc>().add(
              const AllChildrenReportEvent.bulkSave(),
            ),
            icon: const Icon(Icons.cloud_upload_outlined),
          ),
        ],
      ),
      body: BlocBuilder<AllChildrenReportBloc, AllChildrenReportState>(
        builder: (context, state) {
          if (state.children.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else {
            return ListView.builder(
              itemCount: state.children.length,
              itemBuilder: (context, index) {
                final child = state.children[index];
                return ListTile(
                  title: Text(
                    '${child.name.toCapitalize()} ${child.lastName.toCapitalize()}',
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }
}
