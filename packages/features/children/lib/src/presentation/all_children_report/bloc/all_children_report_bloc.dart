import 'package:auth/auth.dart';
import 'package:bloc/bloc.dart';
import 'package:children/children.dart';
import 'package:children/src/domain/models/registered_child.dart';
import 'package:children/src/domain/usecases/get_children_usecase.dart';
import 'package:children/src/domain/usecases/save_bulk_children_usecase.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';
import 'package:to_csv/to_csv.dart';

part 'all_children_report_event.dart';
part 'all_children_report_state.dart';
part 'all_children_report_bloc.freezed.dart';

@injectable
class AllChildrenReportBloc
    extends Bloc<AllChildrenReportEvent, AllChildrenReportState> {
  AllChildrenReportBloc(
    this._getAllUsersUsecase,
    this._getChildrenUsecase,
    this._saveBulkChildrenUsecase,
  ) : super(const AllChildrenReportState()) {
    on<AllChildrenReportEvent>(
      (event, emit) => switch (event) {
        _LoadAllChildrenReportEvent() => _onLoad(emit, event),
        _ExportAllChildrenReportEvent() => _onExportCSV(emit, event),
        _BulkSaveChildrenReportEvent() => _onBulkSave(emit, event),
        _ => null,
      },
    );

    add(const _LoadAllChildrenReportEvent());
  }

  final GetAllUsersUsecase _getAllUsersUsecase;
  final GetChildrenUsecase _getChildrenUsecase;
  final SaveBulkChildrenUsecase _saveBulkChildrenUsecase;

  Future<void> _onLoad(
    Emitter<AllChildrenReportState> emit,
    _LoadAllChildrenReportEvent event,
  ) async {
    emit(state.copyWith(status: const ProcessState.loading()));
    final allUsers = (await _getAllUsersUsecase()).getOrNull();

    if (!allUsers.isNotNullOrEmpty) {
      emit(state.copyWith(status: ProcessState.error(Exception())));
    }

    final children = <Child>[];
    final registeredChildren = <RegisteredChild>[];
    final List<List<String>> childrenData = [];
    var childCount = 1;

    for (final parent in allUsers!) {
      if (!parent.isDummyAccount) {
        final _children = (await _getChildrenUsecase(
          parent.id!,
        )).getOrElse(() => []);
        if (_children.isNotNullOrEmpty) {
          for (final child in _children) {
            childrenData.add([
              (childCount++).toString(),
              ...child.toCsvRow(),
              ...parent.toCsvRow(),
            ]);
            registeredChildren.add(
              RegisteredChild.fromChild(child, parent.id!),
            );
            children.add(child);
          }
        }
      }
    }

    emit(
      state.copyWith(
        status: const ProcessState.loaded(),
        parents: allUsers.toList(),
        registeredChildren: registeredChildren,
        children: children..sort((a, b) => a.lastName.compareTo(b.lastName)),
        csvData: childrenData,
      ),
    );
  }

  Future<void> _onExportCSV(
    Emitter<AllChildrenReportState> emit,
    _ExportAllChildrenReportEvent event,
  ) async {
    await myCSV(
      [
        ...Child.csvHeader(),
        ...UserDetails.csvHeader(),
      ],
      state.csvData,
      showDuplicateValue: true,
      setHeadersInFirstRow: true,
    );
  }

  Future<void> _onBulkSave(
    Emitter<AllChildrenReportState> emit,
    _BulkSaveChildrenReportEvent event,
  ) async {
    final response = await _saveBulkChildrenUsecase(state.registeredChildren);

    response.fold(
      (_) {
        debugPrint('All Done');
      },
      (e) => emit(
        state.copyWith(status: ProcessState.error(e)),
      ),
    );
  }
}
