// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'all_children_report_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AllChildrenReportEvent implements DiagnosticableTreeMixin {




@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AllChildrenReportEvent'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AllChildrenReportEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AllChildrenReportEvent()';
}


}

/// @nodoc
class $AllChildrenReportEventCopyWith<$Res>  {
$AllChildrenReportEventCopyWith(AllChildrenReportEvent _, $Res Function(AllChildrenReportEvent) __);
}


/// @nodoc


class _LoadAllChildrenReportEvent with DiagnosticableTreeMixin implements AllChildrenReportEvent {
  const _LoadAllChildrenReportEvent();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AllChildrenReportEvent.load'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadAllChildrenReportEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AllChildrenReportEvent.load()';
}


}




/// @nodoc


class _ExportAllChildrenReportEvent with DiagnosticableTreeMixin implements AllChildrenReportEvent {
  const _ExportAllChildrenReportEvent();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AllChildrenReportEvent.exportCSV'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ExportAllChildrenReportEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AllChildrenReportEvent.exportCSV()';
}


}




/// @nodoc


class _BulkSaveChildrenReportEvent with DiagnosticableTreeMixin implements AllChildrenReportEvent {
  const _BulkSaveChildrenReportEvent();
  





@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AllChildrenReportEvent.bulkSave'))
    ;
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BulkSaveChildrenReportEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AllChildrenReportEvent.bulkSave()';
}


}




/// @nodoc
mixin _$AllChildrenReportState implements DiagnosticableTreeMixin {

 ProcessState get status; List<UserDetails> get parents; List<Child> get children; List<RegisteredChild> get registeredChildren; List<List<String>> get csvData;
/// Create a copy of AllChildrenReportState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AllChildrenReportStateCopyWith<AllChildrenReportState> get copyWith => _$AllChildrenReportStateCopyWithImpl<AllChildrenReportState>(this as AllChildrenReportState, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AllChildrenReportState'))
    ..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('parents', parents))..add(DiagnosticsProperty('children', children))..add(DiagnosticsProperty('registeredChildren', registeredChildren))..add(DiagnosticsProperty('csvData', csvData));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AllChildrenReportState&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.parents, parents)&&const DeepCollectionEquality().equals(other.children, children)&&const DeepCollectionEquality().equals(other.registeredChildren, registeredChildren)&&const DeepCollectionEquality().equals(other.csvData, csvData));
}


@override
int get hashCode => Object.hash(runtimeType,status,const DeepCollectionEquality().hash(parents),const DeepCollectionEquality().hash(children),const DeepCollectionEquality().hash(registeredChildren),const DeepCollectionEquality().hash(csvData));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AllChildrenReportState(status: $status, parents: $parents, children: $children, registeredChildren: $registeredChildren, csvData: $csvData)';
}


}

/// @nodoc
abstract mixin class $AllChildrenReportStateCopyWith<$Res>  {
  factory $AllChildrenReportStateCopyWith(AllChildrenReportState value, $Res Function(AllChildrenReportState) _then) = _$AllChildrenReportStateCopyWithImpl;
@useResult
$Res call({
 ProcessState status, List<UserDetails> parents, List<Child> children, List<RegisteredChild> registeredChildren, List<List<String>> csvData
});


$ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class _$AllChildrenReportStateCopyWithImpl<$Res>
    implements $AllChildrenReportStateCopyWith<$Res> {
  _$AllChildrenReportStateCopyWithImpl(this._self, this._then);

  final AllChildrenReportState _self;
  final $Res Function(AllChildrenReportState) _then;

/// Create a copy of AllChildrenReportState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? parents = null,Object? children = null,Object? registeredChildren = null,Object? csvData = null,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,parents: null == parents ? _self.parents : parents // ignore: cast_nullable_to_non_nullable
as List<UserDetails>,children: null == children ? _self.children : children // ignore: cast_nullable_to_non_nullable
as List<Child>,registeredChildren: null == registeredChildren ? _self.registeredChildren : registeredChildren // ignore: cast_nullable_to_non_nullable
as List<RegisteredChild>,csvData: null == csvData ? _self.csvData : csvData // ignore: cast_nullable_to_non_nullable
as List<List<String>>,
  ));
}
/// Create a copy of AllChildrenReportState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}


/// @nodoc


class _AllChildrenReportState with DiagnosticableTreeMixin implements AllChildrenReportState {
  const _AllChildrenReportState({this.status = const ProcessState.initial(), final  List<UserDetails> parents = const [], final  List<Child> children = const [], final  List<RegisteredChild> registeredChildren = const [], final  List<List<String>> csvData = const []}): _parents = parents,_children = children,_registeredChildren = registeredChildren,_csvData = csvData;
  

@override@JsonKey() final  ProcessState status;
 final  List<UserDetails> _parents;
@override@JsonKey() List<UserDetails> get parents {
  if (_parents is EqualUnmodifiableListView) return _parents;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_parents);
}

 final  List<Child> _children;
@override@JsonKey() List<Child> get children {
  if (_children is EqualUnmodifiableListView) return _children;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_children);
}

 final  List<RegisteredChild> _registeredChildren;
@override@JsonKey() List<RegisteredChild> get registeredChildren {
  if (_registeredChildren is EqualUnmodifiableListView) return _registeredChildren;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_registeredChildren);
}

 final  List<List<String>> _csvData;
@override@JsonKey() List<List<String>> get csvData {
  if (_csvData is EqualUnmodifiableListView) return _csvData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_csvData);
}


/// Create a copy of AllChildrenReportState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AllChildrenReportStateCopyWith<_AllChildrenReportState> get copyWith => __$AllChildrenReportStateCopyWithImpl<_AllChildrenReportState>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'AllChildrenReportState'))
    ..add(DiagnosticsProperty('status', status))..add(DiagnosticsProperty('parents', parents))..add(DiagnosticsProperty('children', children))..add(DiagnosticsProperty('registeredChildren', registeredChildren))..add(DiagnosticsProperty('csvData', csvData));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AllChildrenReportState&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._parents, _parents)&&const DeepCollectionEquality().equals(other._children, _children)&&const DeepCollectionEquality().equals(other._registeredChildren, _registeredChildren)&&const DeepCollectionEquality().equals(other._csvData, _csvData));
}


@override
int get hashCode => Object.hash(runtimeType,status,const DeepCollectionEquality().hash(_parents),const DeepCollectionEquality().hash(_children),const DeepCollectionEquality().hash(_registeredChildren),const DeepCollectionEquality().hash(_csvData));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'AllChildrenReportState(status: $status, parents: $parents, children: $children, registeredChildren: $registeredChildren, csvData: $csvData)';
}


}

/// @nodoc
abstract mixin class _$AllChildrenReportStateCopyWith<$Res> implements $AllChildrenReportStateCopyWith<$Res> {
  factory _$AllChildrenReportStateCopyWith(_AllChildrenReportState value, $Res Function(_AllChildrenReportState) _then) = __$AllChildrenReportStateCopyWithImpl;
@override @useResult
$Res call({
 ProcessState status, List<UserDetails> parents, List<Child> children, List<RegisteredChild> registeredChildren, List<List<String>> csvData
});


@override $ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class __$AllChildrenReportStateCopyWithImpl<$Res>
    implements _$AllChildrenReportStateCopyWith<$Res> {
  __$AllChildrenReportStateCopyWithImpl(this._self, this._then);

  final _AllChildrenReportState _self;
  final $Res Function(_AllChildrenReportState) _then;

/// Create a copy of AllChildrenReportState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? parents = null,Object? children = null,Object? registeredChildren = null,Object? csvData = null,}) {
  return _then(_AllChildrenReportState(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,parents: null == parents ? _self._parents : parents // ignore: cast_nullable_to_non_nullable
as List<UserDetails>,children: null == children ? _self._children : children // ignore: cast_nullable_to_non_nullable
as List<Child>,registeredChildren: null == registeredChildren ? _self._registeredChildren : registeredChildren // ignore: cast_nullable_to_non_nullable
as List<RegisteredChild>,csvData: null == csvData ? _self._csvData : csvData // ignore: cast_nullable_to_non_nullable
as List<List<String>>,
  ));
}

/// Create a copy of AllChildrenReportState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}

// dart format on
