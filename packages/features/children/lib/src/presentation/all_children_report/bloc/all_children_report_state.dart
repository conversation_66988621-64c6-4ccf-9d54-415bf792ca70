part of 'all_children_report_bloc.dart';

@freezed
abstract class AllChildrenReportState with _$AllChildrenReportState {
  const factory AllChildrenReportState({
    @Default(ProcessState.initial()) ProcessState status,
    @Default([]) List<UserDetails> parents,
    @Default([]) List<Child> children,
    @Default([]) List<RegisteredChild> registeredChildren,
    @Default([]) List<List<String>> csvData,
  }) = _AllChildrenReportState;
}
