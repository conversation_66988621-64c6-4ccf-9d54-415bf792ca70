import 'package:auth/auth.dart';
import 'package:bloc/bloc.dart';
import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/models/week_days.dart';
import 'package:children/src/domain/usecases/update_child_details_usecase.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:prelude/prelude.dart';

part 'child_details_event.dart';
part 'child_details_state.dart';
part 'child_details_bloc.freezed.dart';

@injectable
class ChildDetailsBloc extends Bloc<ChildDetailsEvent, ChildDetailsState> {
  ChildDetailsBloc(
    @factoryParam Child child,
    this._updateChildDetailsUsecase,
    this._getCurrentUserUsecase,
  ) : super(ChildDetailsState(child: child)) {
    on<ChildDetailsEvent>(
      (event, emit) => switch (event) {
        _LoadChildDetailsEvent() => _onLoad(emit, event),
        _ChangeAssistanceChildDetailsEvent() => _onChangeAssistance(
          emit,
          event,
        ),
        _UpdateChildDetailsEvent() => _onUpdateChildDetails(emit, event),
        _ => null,
      },
    );
  }

  final UpdateChildDetailsUsecase _updateChildDetailsUsecase;
  final GetCurrentUserUsecase _getCurrentUserUsecase;

  void _onLoad(
    Emitter<ChildDetailsState> emit,
    _LoadChildDetailsEvent event,
  ) {
    emit(state.copyWith(weekAssitance: state.child.weekAssitance));
  }

  void _onChangeAssistance(
    Emitter<ChildDetailsState> emit,
    _ChangeAssistanceChildDetailsEvent event,
  ) {
    Set<WeekDays> _parseCheckBox(Map<WeekDays, bool>? map) =>
        map?.entries
            .where((entry) => entry.value)
            .map((el) => el.key)
            .toSet() ??
        state.child.weekAssitance;

    emit(state.copyWith(weekAssitance: _parseCheckBox(event.weekDays)));
  }

  Future<void> _onUpdateChildDetails(
    Emitter<ChildDetailsState> emit,
    _UpdateChildDetailsEvent event,
  ) async {
    emit(state.copyWith(status: const ProcessState.loading()));
    final userId = _getCurrentUserUsecase().getOrNull();

    if (state.child.id.isNotNullOrEmpty && userId.isNotNullOrEmpty) {
      await _updateChildDetailsUsecase(
        userId!,
        state.child.id!,
        state.child.copyWith(weekAssitance: state.weekAssitance),
      );
      emit(state.copyWith(status: const ProcessState.loaded()));
    }
  }
}
