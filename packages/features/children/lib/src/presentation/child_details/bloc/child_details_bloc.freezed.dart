// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'child_details_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ChildDetailsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChildDetailsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChildDetailsEvent()';
}


}

/// @nodoc
class $ChildDetailsEventCopyWith<$Res>  {
$ChildDetailsEventCopyWith(ChildDetailsEvent _, $Res Function(ChildDetailsEvent) __);
}


/// @nodoc


class _LoadChildDetailsEvent implements ChildDetailsEvent {
  const _LoadChildDetailsEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoadChildDetailsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChildDetailsEvent.load()';
}


}




/// @nodoc


class _ChangeAssistanceChildDetailsEvent implements ChildDetailsEvent {
  const _ChangeAssistanceChildDetailsEvent(final  Map<WeekDays, bool> weekDays): _weekDays = weekDays;
  

 final  Map<WeekDays, bool> _weekDays;
 Map<WeekDays, bool> get weekDays {
  if (_weekDays is EqualUnmodifiableMapView) return _weekDays;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_weekDays);
}


/// Create a copy of ChildDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeAssistanceChildDetailsEventCopyWith<_ChangeAssistanceChildDetailsEvent> get copyWith => __$ChangeAssistanceChildDetailsEventCopyWithImpl<_ChangeAssistanceChildDetailsEvent>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeAssistanceChildDetailsEvent&&const DeepCollectionEquality().equals(other._weekDays, _weekDays));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_weekDays));

@override
String toString() {
  return 'ChildDetailsEvent.changeAssistance(weekDays: $weekDays)';
}


}

/// @nodoc
abstract mixin class _$ChangeAssistanceChildDetailsEventCopyWith<$Res> implements $ChildDetailsEventCopyWith<$Res> {
  factory _$ChangeAssistanceChildDetailsEventCopyWith(_ChangeAssistanceChildDetailsEvent value, $Res Function(_ChangeAssistanceChildDetailsEvent) _then) = __$ChangeAssistanceChildDetailsEventCopyWithImpl;
@useResult
$Res call({
 Map<WeekDays, bool> weekDays
});




}
/// @nodoc
class __$ChangeAssistanceChildDetailsEventCopyWithImpl<$Res>
    implements _$ChangeAssistanceChildDetailsEventCopyWith<$Res> {
  __$ChangeAssistanceChildDetailsEventCopyWithImpl(this._self, this._then);

  final _ChangeAssistanceChildDetailsEvent _self;
  final $Res Function(_ChangeAssistanceChildDetailsEvent) _then;

/// Create a copy of ChildDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? weekDays = null,}) {
  return _then(_ChangeAssistanceChildDetailsEvent(
null == weekDays ? _self._weekDays : weekDays // ignore: cast_nullable_to_non_nullable
as Map<WeekDays, bool>,
  ));
}


}

/// @nodoc


class _UpdateChildDetailsEvent implements ChildDetailsEvent {
  const _UpdateChildDetailsEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateChildDetailsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'ChildDetailsEvent.updateChild()';
}


}




/// @nodoc
mixin _$ChildDetailsState {

 Child get child; Set<WeekDays> get weekAssitance; ProcessState get status;
/// Create a copy of ChildDetailsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChildDetailsStateCopyWith<ChildDetailsState> get copyWith => _$ChildDetailsStateCopyWithImpl<ChildDetailsState>(this as ChildDetailsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChildDetailsState&&(identical(other.child, child) || other.child == child)&&const DeepCollectionEquality().equals(other.weekAssitance, weekAssitance)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,child,const DeepCollectionEquality().hash(weekAssitance),status);

@override
String toString() {
  return 'ChildDetailsState(child: $child, weekAssitance: $weekAssitance, status: $status)';
}


}

/// @nodoc
abstract mixin class $ChildDetailsStateCopyWith<$Res>  {
  factory $ChildDetailsStateCopyWith(ChildDetailsState value, $Res Function(ChildDetailsState) _then) = _$ChildDetailsStateCopyWithImpl;
@useResult
$Res call({
 Child child, Set<WeekDays> weekAssitance, ProcessState status
});


$ChildCopyWith<$Res> get child;$ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class _$ChildDetailsStateCopyWithImpl<$Res>
    implements $ChildDetailsStateCopyWith<$Res> {
  _$ChildDetailsStateCopyWithImpl(this._self, this._then);

  final ChildDetailsState _self;
  final $Res Function(ChildDetailsState) _then;

/// Create a copy of ChildDetailsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? child = null,Object? weekAssitance = null,Object? status = null,}) {
  return _then(_self.copyWith(
child: null == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as Child,weekAssitance: null == weekAssitance ? _self.weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Set<WeekDays>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,
  ));
}
/// Create a copy of ChildDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChildCopyWith<$Res> get child {
  
  return $ChildCopyWith<$Res>(_self.child, (value) {
    return _then(_self.copyWith(child: value));
  });
}/// Create a copy of ChildDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}


/// @nodoc


class _ChildDetailsState implements ChildDetailsState {
  const _ChildDetailsState({required this.child, final  Set<WeekDays> weekAssitance = const {}, this.status = const ProcessState.initial()}): _weekAssitance = weekAssitance;
  

@override final  Child child;
 final  Set<WeekDays> _weekAssitance;
@override@JsonKey() Set<WeekDays> get weekAssitance {
  if (_weekAssitance is EqualUnmodifiableSetView) return _weekAssitance;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableSetView(_weekAssitance);
}

@override@JsonKey() final  ProcessState status;

/// Create a copy of ChildDetailsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChildDetailsStateCopyWith<_ChildDetailsState> get copyWith => __$ChildDetailsStateCopyWithImpl<_ChildDetailsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChildDetailsState&&(identical(other.child, child) || other.child == child)&&const DeepCollectionEquality().equals(other._weekAssitance, _weekAssitance)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,child,const DeepCollectionEquality().hash(_weekAssitance),status);

@override
String toString() {
  return 'ChildDetailsState(child: $child, weekAssitance: $weekAssitance, status: $status)';
}


}

/// @nodoc
abstract mixin class _$ChildDetailsStateCopyWith<$Res> implements $ChildDetailsStateCopyWith<$Res> {
  factory _$ChildDetailsStateCopyWith(_ChildDetailsState value, $Res Function(_ChildDetailsState) _then) = __$ChildDetailsStateCopyWithImpl;
@override @useResult
$Res call({
 Child child, Set<WeekDays> weekAssitance, ProcessState status
});


@override $ChildCopyWith<$Res> get child;@override $ProcessStateCopyWith<$Res> get status;

}
/// @nodoc
class __$ChildDetailsStateCopyWithImpl<$Res>
    implements _$ChildDetailsStateCopyWith<$Res> {
  __$ChildDetailsStateCopyWithImpl(this._self, this._then);

  final _ChildDetailsState _self;
  final $Res Function(_ChildDetailsState) _then;

/// Create a copy of ChildDetailsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? child = null,Object? weekAssitance = null,Object? status = null,}) {
  return _then(_ChildDetailsState(
child: null == child ? _self.child : child // ignore: cast_nullable_to_non_nullable
as Child,weekAssitance: null == weekAssitance ? _self._weekAssitance : weekAssitance // ignore: cast_nullable_to_non_nullable
as Set<WeekDays>,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ProcessState,
  ));
}

/// Create a copy of ChildDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChildCopyWith<$Res> get child {
  
  return $ChildCopyWith<$Res>(_self.child, (value) {
    return _then(_self.copyWith(child: value));
  });
}/// Create a copy of ChildDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProcessStateCopyWith<$Res> get status {
  
  return $ProcessStateCopyWith<$Res>(_self.status, (value) {
    return _then(_self.copyWith(status: value));
  });
}
}

// dart format on
