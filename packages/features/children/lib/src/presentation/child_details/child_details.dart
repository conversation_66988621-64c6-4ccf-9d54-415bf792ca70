import 'package:app_di/app_di.dart';
import 'package:children/src/domain/models/child.dart';
import 'package:children/src/domain/models/school_year.dart';
import 'package:children/src/presentation/child_details/bloc/child_details_bloc.dart';
import 'package:children/src/presentation/register_child/register_child_page.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ChildDetails extends StatelessWidget {
  const ChildDetails({
    required this.child,
    super.key,
  });

  final Child child;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => diContainer<ChildDetailsBloc>(parameter: child),
      child: const _Content(),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return BlocBuilder<ChildDetailsBloc, ChildDetailsState>(
      builder: (context, state) {
        return Scaffold(
          bottomSheet: const _BottomButtonsSheet(),
          appBar: AppBar(
            title: const Text('Detalles'),
          ),
          body: Padding(
            padding: dimen.all.xs,
            child: Column(
              children: [
                Text(
                  '${state.child.name} ${state.child.lastName}',
                  style: theme.appTextTheme.headline3,
                ),
                Text(
                  '${state.child.schoolYear.getName()}',
                  style: theme.appTextTheme.headline6,
                ),
                const SizedBox(height: xs),
                Text(
                  'Modificar Asistencia',
                  style: theme.appTextTheme.headline5,
                ),
                const SizedBox(height: xxs),
                WeekdaysCheckboxGroup(
                  weekDays: state.child.weekAssitance,
                  onDayChanged: (value) => context.read<ChildDetailsBloc>().add(
                    ChildDetailsEvent.changeAssistance(value),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _BottomButtonsSheet extends StatelessWidget {
  const _BottomButtonsSheet();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChildDetailsBloc, ChildDetailsState>(
      builder: (context, state) {
        return SafeArea(
          child: Padding(
            padding: dimen.all.xs,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                PrimaryButton.responsive(
                  title: 'Guardar',
                  isLoading: state.status.isLoading,
                  onPressed: state.isValid
                      ? () => context.read<ChildDetailsBloc>().add(
                          const ChildDetailsEvent.updateChild(),
                        )
                      : null,
                ),
                const SizedBox(height: xs),
              ],
            ),
          ),
        );
      },
    );
  }
}
