// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'process_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProcessState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ProcessState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProcessState()';
  }
}

/// @nodoc
class $ProcessStateCopyWith<$Res> {
  $ProcessStateCopyWith(ProcessState _, $Res Function(ProcessState) __);
}

/// @nodoc

class InitialProcessState implements ProcessState {
  const InitialProcessState();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is InitialProcessState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProcessState.initial()';
  }
}

/// @nodoc

class LoadingProcessState implements ProcessState {
  const LoadingProcessState();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LoadingProcessState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProcessState.loading()';
  }
}

/// @nodoc

class LoadedProcessState implements ProcessState {
  const LoadedProcessState();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LoadedProcessState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProcessState.loaded()';
  }
}

/// @nodoc

class ErrorProcessState implements ProcessState {
  const ErrorProcessState(this.error);

  final Exception error;

  /// Create a copy of ProcessState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ErrorProcessStateCopyWith<ErrorProcessState> get copyWith =>
      _$ErrorProcessStateCopyWithImpl<ErrorProcessState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ErrorProcessState &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'ProcessState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class $ErrorProcessStateCopyWith<$Res>
    implements $ProcessStateCopyWith<$Res> {
  factory $ErrorProcessStateCopyWith(
          ErrorProcessState value, $Res Function(ErrorProcessState) _then) =
      _$ErrorProcessStateCopyWithImpl;
  @useResult
  $Res call({Exception error});
}

/// @nodoc
class _$ErrorProcessStateCopyWithImpl<$Res>
    implements $ErrorProcessStateCopyWith<$Res> {
  _$ErrorProcessStateCopyWithImpl(this._self, this._then);

  final ErrorProcessState _self;
  final $Res Function(ErrorProcessState) _then;

  /// Create a copy of ProcessState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = null,
  }) {
    return _then(ErrorProcessState(
      null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as Exception,
    ));
  }
}

// dart format on
