import 'package:freezed_annotation/freezed_annotation.dart';

part 'process_state.freezed.dart';

@freezed
sealed class ProcessState with _$ProcessState {
  const factory ProcessState.initial() = InitialProcessState;
  const factory ProcessState.loading() = LoadingProcessState;
  const factory ProcessState.loaded() = LoadedProcessState;
  const factory ProcessState.error(Exception error) = ErrorProcessState;
}

extension ProcessStateX on ProcessState {
  bool get isLoading => switch (this) {
    LoadingProcessState() => true,
    _ => false,
  };
  bool get isLoaded => switch (this) {
    LoadedProcessState() => true,
    _ => false,
  };
  bool get isError => switch (this) {
    ErrorProcessState() => true,
    _ => false,
  };
}
