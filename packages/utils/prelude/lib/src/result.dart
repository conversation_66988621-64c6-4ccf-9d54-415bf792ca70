import 'dart:async';

import 'package:flutter/material.dart';

abstract class Result<S, F> {
  const Result._();

  B fold<B>(B Function(S s) ifSuccess, B Function(F f) ifFailure);

  bool isSuccess() => fold((_) => true, (_) => false);

  bool ifFailure() => fold((_) => false, (_) => true);

  Result<S2, F> map<S2>(S2 Function(S s) f) =>
      fold((S s) => success(f(s)), failure);

  S getOrElse(S Function() defaultValue) =>
      fold((s) => s, (_) => defaultValue());

  S? getOrNull() => fold((s) => s, (_) => null);

  static Future<Result<T, Exception>> fromAsync<T>(
    Future<T> Function() func,
  ) async {
    try {
      final result = await func();
      return success(result);
    } on Exception catch (e) {
      debugPrint(e.toString());
      return failure(e);
      // ignore: avoid_catching_errors
    } on Error catch (e) {
      debugPrintStack(stackTrace: e.stackTrace);
      return failure(Exception('${e.runtimeType}: ${e.stackTrace}'));
    }
  }

  static Result<T, Exception> fromAction<T>(
    T Function() func,
  ) {
    try {
      final result = func();
      return success(result);
    } on Exception catch (e) {
      return failure(e);
      // ignore: avoid_catching_errors
    } on Error catch (e) {
      return failure(Exception('${e.runtimeType}: ${e.stackTrace}'));
    }
  }

  static Result<T, Exception> fromNullable<T>(
    T? value,
    Exception Function() onError, [
    bool Function(T)? predicate,
  ]) {
    if (value != null && (predicate?.call(value) ?? true)) {
      return success(value);
    } else {
      return failure(onError());
    }
  }

  static Result<T, Exception> doSync<T>(
    T Function(T Function<T>(Result<T, Exception>)) f,
  ) => Result.fromAction(() => f(_doCall));

  static Future<Result<T, Exception>> doAsync<T>(
    Future<T> Function(FutureOr<T> Function<T>(FutureOr<Result<T, Exception>>))
    f,
  ) => Result.fromAsync(() => f(_doAsyncCall));
}

class Failure<S, F> extends Result<S, F> {
  const Failure._(this._f) : super._();

  final F _f;

  F get value => _f;

  @override
  B fold<B>(B Function(S s) ifSuccess, B Function(F f) ifFailure) =>
      ifFailure(_f);

  @override
  bool operator ==(Object other) => other is Failure && other._f == _f;

  @override
  int get hashCode => _f.hashCode;
}

class Success<S, F> extends Result<S, F> {
  const Success._(this._s) : super._();

  final S _s;

  S get value => _s;

  @override
  B fold<B>(B Function(S s) ifSuccess, B Function(F f) ifFailure) =>
      ifSuccess(_s);

  @override
  bool operator ==(Object other) => other is Success && other._s == _s;

  @override
  int get hashCode => _s.hashCode;
}

Result<S, F> failure<S, F>(F f) => Failure._(f);
Result<S, F> success<S, F>(S s) => Success._(s);

T _doCall<T>(Result<T, Exception> result) =>
    result.fold((s) => s, (f) => throw f);

FutureOr<T> _doAsyncCall<T>(FutureOr<Result<T, Exception>> result) async {
  return _doCall(await result);
}
