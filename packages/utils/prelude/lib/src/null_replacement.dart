extension StringNullReplacement on String? {
  bool get isNotNullOrEmpty => this != null && this!.isNotEmpty;
}

extension ObjectX on Object? {
  bool get isNotNull => this != null;
}

extension ArrayNullReplacement<T> on Iterable<T>? {
  bool get isNotNullOrEmpty => this != null && this!.isNotEmpty;
}

extension StringExtension on String {
  String toCapitalize() {
    final words = trim().split(' ').map((el) => _capitalize(el));
    return words.join(' ');
  }

  String _capitalize(String el) {
    return '${el[0].toUpperCase()}${el.substring(1).toLowerCase()}';
  }
}

extension BoolX on bool {
  String toLocalString() => this ? 'Si' : 'No';
}
