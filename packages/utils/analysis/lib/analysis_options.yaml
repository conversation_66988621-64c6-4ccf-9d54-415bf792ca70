analyzer:
  exclude:
    - lib/**.g.dart
    - lib/**.freezed.dart

  language:
    strict-casts: true
    strict-raw-types: true

  errors:
    invalid_annotation_target: ignore
    missing_required_param: error
    missing_return: error
    invalid_assignment: warning

linter:
  rules:
    avoid_print: true
    prefer_single_quotes: true
    require_trailing_commas: true
    always_declare_return_types: true
    avoid_void_async: true
    always_use_package_imports: true
    directives_ordering: true
    sort_constructors_first: true
    sort_pub_dependencies: true
    avoid_redundant_argument_values: true
    always_put_required_named_parameters_first: true
    curly_braces_in_flow_control_structures: true
    prefer_const_declarations: true
    avoid_unnecessary_containers: true
    avoid_web_libraries_in_flutter: true
    no_logic_in_create_state: true
    prefer_const_constructors: true
    prefer_const_constructors_in_immutables: true
    prefer_const_literals_to_create_immutables: true
    sized_box_for_whitespace: true
    use_build_context_synchronously: true
    implementation_imports: true
    avoid_catching_errors: true

formatter:
  trailing_commas: preserve