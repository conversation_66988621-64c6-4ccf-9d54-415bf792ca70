import 'package:injectable/injectable.dart';

const diInitializerInit = DiInitializerInit();

class DiInitializerInit extends InjectableInit {
  const DiInitializerInit({
    bool showUnregisteredTypesWarning = false,
    List<String> generatedForDir = const ['lib'],
  }) : super(
         generateForDir: generatedForDir,
         ignoreUnregisteredTypesInPackages: showUnregisteredTypesWarning
             ? const []
             : const ['  '],
       );
}
