import 'package:app_di/src/di_initializer.dart';
import 'package:get_it/get_it.dart';

final diContainer = _LMClientDIContainer(_getIt);

Future<DIContainer> initializeDIContainer(
  List<ClientDIInitializer> initializers, {
  bool shouldReset = true,
  void Function(String)? onInitializationStarted,
}) async {
  if (shouldReset) {
    await _getIt.reset();
  }
  for (final initializer in initializers) {
    onInitializationStarted?.call(initializer.toString());
    await initializer.init(_getIt);
  }
  return diContainer;
}

abstract class DIContainer {
  T call<T extends Object>({dynamic parameter, String? name});

  bool isRegistered<T extends Object>({String? name});
}

final _getIt = GetIt.instance;

class _LMClientDIContainer implements DIContainer {
  const _LMClientDIContainer(this._getIt);

  final GetIt _getIt;

  @override
  T call<T extends Object>({dynamic parameter, String? name}) =>
      _getIt<T>(param1: parameter, instanceName: name);

  @override
  bool isRegistered<T extends Object>({String? name}) =>
      _getIt.isRegistered<T>(instanceName: name);
}
