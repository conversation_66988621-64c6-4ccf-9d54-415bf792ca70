import 'dart:async';

import 'package:app_di/app_di.dart';
import 'package:flavors/src/domain/client_define.dart';
import 'package:flavors/src/domain/model/flavor_config.dart';
import 'package:get_it/get_it.dart';

class ClientFlavorsDIInitializer extends ClientDIInitializer {
  ClientFlavorsDIInitializer(FlavorConfig config)
    : super((getIt, _) => _init(getIt, config));

  static FutureOr<GetIt> _init(GetIt getIt, FlavorConfig config) {
    _registerEvironmentConfig(getIt, config.environmentConfig);
    return getIt;
  }

  static void _registerEvironmentConfig(
    GetIt getIt,
    EnvironmentConfig environmentConfig,
  ) => getIt
    ..registerSingleton(
      ClientDefineEnvironment.environmentName,
      instanceName: CoreDiParameterName.environmentName,
    );
}
