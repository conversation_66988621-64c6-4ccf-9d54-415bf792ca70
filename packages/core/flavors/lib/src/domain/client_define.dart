import 'package:flavors/src/domain/client_environment.dart';

abstract class ClientDefineEnvironment {
  static const environmentName = String.fromEnvironment('ENV');
  static final environment = ClientEnvironment.fromName(environmentName);
  static const environmentIsWip = environmentName == ClientEnvironmentName.wip;
  static const environmentIsProd =
      environmentName == ClientEnvironmentName.prod;
}
