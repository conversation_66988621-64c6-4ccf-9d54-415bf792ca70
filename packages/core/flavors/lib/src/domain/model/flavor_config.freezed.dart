// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flavor_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$FlavorConfig {

 Flavor get flavorName; EnvironmentConfig get environmentConfig;
/// Create a copy of FlavorConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FlavorConfigCopyWith<FlavorConfig> get copyWith => _$FlavorConfigCopyWithImpl<FlavorConfig>(this as FlavorConfig, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FlavorConfig&&(identical(other.flavorName, flavorName) || other.flavorName == flavorName)&&(identical(other.environmentConfig, environmentConfig) || other.environmentConfig == environmentConfig));
}


@override
int get hashCode => Object.hash(runtimeType,flavorName,environmentConfig);

@override
String toString() {
  return 'FlavorConfig(flavorName: $flavorName, environmentConfig: $environmentConfig)';
}


}

/// @nodoc
abstract mixin class $FlavorConfigCopyWith<$Res>  {
  factory $FlavorConfigCopyWith(FlavorConfig value, $Res Function(FlavorConfig) _then) = _$FlavorConfigCopyWithImpl;
@useResult
$Res call({
 Flavor flavorName, EnvironmentConfig environmentConfig
});


$EnvironmentConfigCopyWith<$Res> get environmentConfig;

}
/// @nodoc
class _$FlavorConfigCopyWithImpl<$Res>
    implements $FlavorConfigCopyWith<$Res> {
  _$FlavorConfigCopyWithImpl(this._self, this._then);

  final FlavorConfig _self;
  final $Res Function(FlavorConfig) _then;

/// Create a copy of FlavorConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? flavorName = null,Object? environmentConfig = null,}) {
  return _then(_self.copyWith(
flavorName: null == flavorName ? _self.flavorName : flavorName // ignore: cast_nullable_to_non_nullable
as Flavor,environmentConfig: null == environmentConfig ? _self.environmentConfig : environmentConfig // ignore: cast_nullable_to_non_nullable
as EnvironmentConfig,
  ));
}
/// Create a copy of FlavorConfig
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EnvironmentConfigCopyWith<$Res> get environmentConfig {
  
  return $EnvironmentConfigCopyWith<$Res>(_self.environmentConfig, (value) {
    return _then(_self.copyWith(environmentConfig: value));
  });
}
}


/// @nodoc


class _FlavorConfig implements FlavorConfig {
  const _FlavorConfig({required this.flavorName, required this.environmentConfig});
  

@override final  Flavor flavorName;
@override final  EnvironmentConfig environmentConfig;

/// Create a copy of FlavorConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FlavorConfigCopyWith<_FlavorConfig> get copyWith => __$FlavorConfigCopyWithImpl<_FlavorConfig>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FlavorConfig&&(identical(other.flavorName, flavorName) || other.flavorName == flavorName)&&(identical(other.environmentConfig, environmentConfig) || other.environmentConfig == environmentConfig));
}


@override
int get hashCode => Object.hash(runtimeType,flavorName,environmentConfig);

@override
String toString() {
  return 'FlavorConfig(flavorName: $flavorName, environmentConfig: $environmentConfig)';
}


}

/// @nodoc
abstract mixin class _$FlavorConfigCopyWith<$Res> implements $FlavorConfigCopyWith<$Res> {
  factory _$FlavorConfigCopyWith(_FlavorConfig value, $Res Function(_FlavorConfig) _then) = __$FlavorConfigCopyWithImpl;
@override @useResult
$Res call({
 Flavor flavorName, EnvironmentConfig environmentConfig
});


@override $EnvironmentConfigCopyWith<$Res> get environmentConfig;

}
/// @nodoc
class __$FlavorConfigCopyWithImpl<$Res>
    implements _$FlavorConfigCopyWith<$Res> {
  __$FlavorConfigCopyWithImpl(this._self, this._then);

  final _FlavorConfig _self;
  final $Res Function(_FlavorConfig) _then;

/// Create a copy of FlavorConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? flavorName = null,Object? environmentConfig = null,}) {
  return _then(_FlavorConfig(
flavorName: null == flavorName ? _self.flavorName : flavorName // ignore: cast_nullable_to_non_nullable
as Flavor,environmentConfig: null == environmentConfig ? _self.environmentConfig : environmentConfig // ignore: cast_nullable_to_non_nullable
as EnvironmentConfig,
  ));
}

/// Create a copy of FlavorConfig
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EnvironmentConfigCopyWith<$Res> get environmentConfig {
  
  return $EnvironmentConfigCopyWith<$Res>(_self.environmentConfig, (value) {
    return _then(_self.copyWith(environmentConfig: value));
  });
}
}

/// @nodoc
mixin _$NetworkConfig {

 String get baseUrl;
/// Create a copy of NetworkConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NetworkConfigCopyWith<NetworkConfig> get copyWith => _$NetworkConfigCopyWithImpl<NetworkConfig>(this as NetworkConfig, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetworkConfig&&(identical(other.baseUrl, baseUrl) || other.baseUrl == baseUrl));
}


@override
int get hashCode => Object.hash(runtimeType,baseUrl);

@override
String toString() {
  return 'NetworkConfig(baseUrl: $baseUrl)';
}


}

/// @nodoc
abstract mixin class $NetworkConfigCopyWith<$Res>  {
  factory $NetworkConfigCopyWith(NetworkConfig value, $Res Function(NetworkConfig) _then) = _$NetworkConfigCopyWithImpl;
@useResult
$Res call({
 String baseUrl
});




}
/// @nodoc
class _$NetworkConfigCopyWithImpl<$Res>
    implements $NetworkConfigCopyWith<$Res> {
  _$NetworkConfigCopyWithImpl(this._self, this._then);

  final NetworkConfig _self;
  final $Res Function(NetworkConfig) _then;

/// Create a copy of NetworkConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? baseUrl = null,}) {
  return _then(_self.copyWith(
baseUrl: null == baseUrl ? _self.baseUrl : baseUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _NetworkConfig implements NetworkConfig {
  const _NetworkConfig({required this.baseUrl});
  

@override final  String baseUrl;

/// Create a copy of NetworkConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NetworkConfigCopyWith<_NetworkConfig> get copyWith => __$NetworkConfigCopyWithImpl<_NetworkConfig>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NetworkConfig&&(identical(other.baseUrl, baseUrl) || other.baseUrl == baseUrl));
}


@override
int get hashCode => Object.hash(runtimeType,baseUrl);

@override
String toString() {
  return 'NetworkConfig(baseUrl: $baseUrl)';
}


}

/// @nodoc
abstract mixin class _$NetworkConfigCopyWith<$Res> implements $NetworkConfigCopyWith<$Res> {
  factory _$NetworkConfigCopyWith(_NetworkConfig value, $Res Function(_NetworkConfig) _then) = __$NetworkConfigCopyWithImpl;
@override @useResult
$Res call({
 String baseUrl
});




}
/// @nodoc
class __$NetworkConfigCopyWithImpl<$Res>
    implements _$NetworkConfigCopyWith<$Res> {
  __$NetworkConfigCopyWithImpl(this._self, this._then);

  final _NetworkConfig _self;
  final $Res Function(_NetworkConfig) _then;

/// Create a copy of NetworkConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? baseUrl = null,}) {
  return _then(_NetworkConfig(
baseUrl: null == baseUrl ? _self.baseUrl : baseUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$EnvironmentConfig {

 NetworkConfig get networkConfig;
/// Create a copy of EnvironmentConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EnvironmentConfigCopyWith<EnvironmentConfig> get copyWith => _$EnvironmentConfigCopyWithImpl<EnvironmentConfig>(this as EnvironmentConfig, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EnvironmentConfig&&(identical(other.networkConfig, networkConfig) || other.networkConfig == networkConfig));
}


@override
int get hashCode => Object.hash(runtimeType,networkConfig);

@override
String toString() {
  return 'EnvironmentConfig(networkConfig: $networkConfig)';
}


}

/// @nodoc
abstract mixin class $EnvironmentConfigCopyWith<$Res>  {
  factory $EnvironmentConfigCopyWith(EnvironmentConfig value, $Res Function(EnvironmentConfig) _then) = _$EnvironmentConfigCopyWithImpl;
@useResult
$Res call({
 NetworkConfig networkConfig
});


$NetworkConfigCopyWith<$Res> get networkConfig;

}
/// @nodoc
class _$EnvironmentConfigCopyWithImpl<$Res>
    implements $EnvironmentConfigCopyWith<$Res> {
  _$EnvironmentConfigCopyWithImpl(this._self, this._then);

  final EnvironmentConfig _self;
  final $Res Function(EnvironmentConfig) _then;

/// Create a copy of EnvironmentConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? networkConfig = null,}) {
  return _then(_self.copyWith(
networkConfig: null == networkConfig ? _self.networkConfig : networkConfig // ignore: cast_nullable_to_non_nullable
as NetworkConfig,
  ));
}
/// Create a copy of EnvironmentConfig
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$NetworkConfigCopyWith<$Res> get networkConfig {
  
  return $NetworkConfigCopyWith<$Res>(_self.networkConfig, (value) {
    return _then(_self.copyWith(networkConfig: value));
  });
}
}


/// @nodoc


class _EnvironmentConfig implements EnvironmentConfig {
  const _EnvironmentConfig({required this.networkConfig});
  

@override final  NetworkConfig networkConfig;

/// Create a copy of EnvironmentConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EnvironmentConfigCopyWith<_EnvironmentConfig> get copyWith => __$EnvironmentConfigCopyWithImpl<_EnvironmentConfig>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EnvironmentConfig&&(identical(other.networkConfig, networkConfig) || other.networkConfig == networkConfig));
}


@override
int get hashCode => Object.hash(runtimeType,networkConfig);

@override
String toString() {
  return 'EnvironmentConfig(networkConfig: $networkConfig)';
}


}

/// @nodoc
abstract mixin class _$EnvironmentConfigCopyWith<$Res> implements $EnvironmentConfigCopyWith<$Res> {
  factory _$EnvironmentConfigCopyWith(_EnvironmentConfig value, $Res Function(_EnvironmentConfig) _then) = __$EnvironmentConfigCopyWithImpl;
@override @useResult
$Res call({
 NetworkConfig networkConfig
});


@override $NetworkConfigCopyWith<$Res> get networkConfig;

}
/// @nodoc
class __$EnvironmentConfigCopyWithImpl<$Res>
    implements _$EnvironmentConfigCopyWith<$Res> {
  __$EnvironmentConfigCopyWithImpl(this._self, this._then);

  final _EnvironmentConfig _self;
  final $Res Function(_EnvironmentConfig) _then;

/// Create a copy of EnvironmentConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? networkConfig = null,}) {
  return _then(_EnvironmentConfig(
networkConfig: null == networkConfig ? _self.networkConfig : networkConfig // ignore: cast_nullable_to_non_nullable
as NetworkConfig,
  ));
}

/// Create a copy of EnvironmentConfig
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$NetworkConfigCopyWith<$Res> get networkConfig {
  
  return $NetworkConfigCopyWith<$Res>(_self.networkConfig, (value) {
    return _then(_self.copyWith(networkConfig: value));
  });
}
}

// dart format on
