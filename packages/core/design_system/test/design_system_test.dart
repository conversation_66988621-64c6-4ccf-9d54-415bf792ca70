import 'package:design_system/design_system.dart';
import 'package:design_system/src/molecules/stepper_progress/stepper_progress.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Golden Tests for Design System', () {
    testWidgets('PrimaryButton renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            body: PrimaryButton.responsive(
              title: 'Test Button',
              onPressed: () {},
            ),
          ),
        ),
      );

      await expectLater(
        find.byType(PrimaryButton),
        matchesGoldenFile('goldens/primary_button.png'),
      );
    });

    testWidgets('SecondaryButton renders correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            body: SecondaryButton.responsive(
              title: 'Test Button',
              onPressed: () {},
            ),
          ),
        ),
      );

      await expectLater(
        find.byType(SecondaryButton),
        matchesGoldenFile('goldens/secondary_button.png'),
      );
    });

    testWidgets('StoryAvatar renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            body: StoryAvatar(
              image: const AssetImage('assets/avatar.png'),
              onTap: () {},
              label: 'Test Avatar',
              hasNewStories: true,
              status: StoryAvatarStatus.online,
            ),
          ),
        ),
      );

      await expectLater(
        find.byType(StoryAvatar),
        matchesGoldenFile('goldens/story_avatar.png'),
      );
    });

    testWidgets('AppBottomNavbar renders correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            bottomNavigationBar: AppBottomNavbar(
              currentIndex: 0,
              items: const [
                AppBottomNavbarItem(
                  label: 'Home',
                  icon: Icon(Icons.home),
                  activeIcon: Icon(Icons.home_filled),
                ),
                AppBottomNavbarItem(
                  label: 'Profile',
                  icon: Icon(Icons.person_outline),
                  activeIcon: Icon(Icons.person),
                  showBadge: true,
                ),
              ],
              onTap: (index) {},
            ),
          ),
        ),
      );

      await expectLater(
        find.byType(AppBottomNavbar),
        matchesGoldenFile('goldens/app_bottom_navbar.png'),
      );
    });

    testWidgets('StepperProgress renders correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: const Scaffold(
            body: StepperProgress(
              totalSteps: 5,
              currentStep: 2,
            ),
          ),
        ),
      );

      await expectLater(
        find.byType(StepperProgress),
        matchesGoldenFile('goldens/stepper_progress.png'),
      );
    });

    testWidgets('GroupCard renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.light,
          home: Scaffold(
            body: GroupCard(
              image: const AssetImage('assets/group_image.png'),
              title: 'Test Group',
              description: 'This is a test group card.',
              onTap: () {},
              isFavorite: true,
            ),
          ),
        ),
      );

      await expectLater(
        find.byType(GroupCard),
        matchesGoldenFile('goldens/group_card.png'),
      );
    });
  });
}
