import 'package:design_system/src/atoms/buttons/base_button.dart';
import 'package:design_system/src/atoms/buttons/button_size.dart';
import 'package:design_system/src/atoms/dimensions.dart';
import 'package:design_system/src/atoms/sizes.dart';
import 'package:design_system/src/theme/theme.dart';
import 'package:flutter/material.dart';

class AccentButton extends StatelessWidget {
  const AccentButton._({
    required this.title,
    required this.isLoading,
    required this.buttonSize,
    super.key,
    this.onPressed,
    this.leadingIcon,
    this.trailingIcon,
  });

  factory AccentButton.responsive({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
  }) => AccentButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    buttonSize: ButtonSize.responsive,
  );

  factory AccentButton.blocked({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
  }) => AccentButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    buttonSize: ButtonSize.blocked,
  );

  factory AccentButton.small({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
  }) => AccentButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    buttonSize: ButtonSize.small,
  );

  factory AccentButton.bigBlocked({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
  }) => AccentButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    buttonSize: ButtonSize.bigBlocked,
  );

  factory AccentButton.bigResponsive({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
  }) => AccentButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    buttonSize: ButtonSize.bigResponsive,
  );

  final VoidCallback? onPressed;
  final String title;
  final bool isLoading;
  final ButtonSize buttonSize;
  final Widget? leadingIcon;
  final Widget? trailingIcon;

  Widget _getLabel(BuildContext context) =>
      isLoading ? _getLoadingIndicator() : _getLabelWidget(context);

  Widget _getLoadingIndicator() => const SizedBox.square(
    dimension: sm,
    child: CircularProgressIndicator(color: Colors.white),
  );

  Color? _getStyle(Set<WidgetState> states, BuildContext context) {
    if (states.contains(WidgetState.disabled)) {
      return isLoading
          ? context.appTheme.semanticColorsTheme.greyShades.shade900.withAlpha(
              100,
            )
          : context.appTheme.disabledColor;
    }
    return context
        .appTheme
        .semanticColorsTheme
        .greyShades
        .shade900; // Use the component's default.
  }

  Widget _getLabelWidget(BuildContext context) => Row(
    mainAxisAlignment: MainAxisAlignment.center,
    mainAxisSize: MainAxisSize.min,
    children: [
      leadingIcon != null
          ? Padding(
              padding: dimen.left.xxs,
              child: leadingIcon!,
            )
          : const SizedBox(width: xxs),
      Padding(
        padding: dimen.x.xxs,
        child: Text(
          title,
          textAlign: TextAlign.center,
        ),
      ),
      trailingIcon != null
          ? Padding(
              padding: dimen.right.xxs,
              child: trailingIcon!,
            )
          : const SizedBox(width: xxs),
    ],
  );

  @override
  Widget build(BuildContext context) {
    final button = FilledButton(
      onPressed: !isLoading ? onPressed : null,
      style: ButtonStyle(
        padding: WidgetStatePropertyAll<EdgeInsets?>(
          innerButtonPadding(buttonSize),
        ),
        backgroundColor: WidgetStateProperty.resolveWith<Color?>(
          (states) => _getStyle(states, context),
        ),
        iconSize: const WidgetStatePropertyAll<double?>(xs),
      ),
      child: _getLabel(context),
    );

    return switch (buttonSize) {
      (ButtonSize.responsive || ButtonSize.bigResponsive) => Row(
        children: [
          Expanded(child: button),
        ],
      ),
      _ => button,
    };
  }
}
