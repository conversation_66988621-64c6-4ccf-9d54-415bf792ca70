import 'package:design_system/src/atoms/buttons/base_button.dart';
import 'package:design_system/src/atoms/buttons/button_size.dart';
import 'package:design_system/src/atoms/dimensions.dart';
import 'package:design_system/src/atoms/sizes.dart';
import 'package:design_system/src/theme/theme.dart';
import 'package:flutter/material.dart';

class SecondaryButton extends StatelessWidget {
  const SecondaryButton._({
    required this.title,
    required this.isLoading,
    required this.buttonSize,
    super.key,
    this.onPressed,
    this.leadingIcon,
    this.trailingIcon,
  });

  factory SecondaryButton.responsive({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
  }) => SecondaryButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    buttonSize: ButtonSize.responsive,
  );

  factory SecondaryButton.blocked({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
  }) => SecondaryButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    buttonSize: ButtonSize.blocked,
  );

  factory SecondaryButton.small({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
  }) => SecondaryButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    buttonSize: ButtonSize.small,
  );

  final VoidCallback? onPressed;
  final String title;
  final bool isLoading;
  final ButtonSize buttonSize;
  final Widget? leadingIcon;
  final Widget? trailingIcon;

  Widget _getLabel(BuildContext context) =>
      isLoading ? _getLoadingIndicator(context) : _getLabelWidget();

  Widget _getLoadingIndicator(BuildContext context) => SizedBox.square(
    dimension: sm,
    child: CircularProgressIndicator(
      color: context.appTheme.colorScheme.primary,
    ),
  );

  Color? _getStyle(Set<WidgetState> states, BuildContext context) {
    if (states.contains(WidgetState.disabled)) {
      return isLoading
          ? context.appTheme.colorScheme.surface
          : context.appTheme.disabledColor;
    }
    return null; // Use the component's default.
  }

  Color? _getForegroundStyle(Set<WidgetState> states, BuildContext context) =>
      states.contains(WidgetState.disabled)
      ? context.appTheme.semanticColorsTheme.greyShades.shade150
      : context.appTheme.colorScheme.onSecondary;

  Color? _getIconStyle(Set<WidgetState> states, BuildContext context) =>
      states.contains(WidgetState.disabled)
      ? null
      : context.appTheme.colorScheme.primary;

  Widget _getLabelWidget() => Row(
    mainAxisAlignment: MainAxisAlignment.center,
    mainAxisSize: MainAxisSize.min,
    children: [
      leadingIcon != null
          ? Padding(
              padding: dimen.left.xxs,
              child: leadingIcon!,
            )
          : const SizedBox(width: xxs),
      Padding(
        padding: dimen.x.xxs,
        child: Text(
          title,
          textAlign: TextAlign.center,
        ),
      ),
      trailingIcon != null
          ? Padding(
              padding: dimen.right.xxs,
              child: trailingIcon!,
            )
          : const SizedBox(width: xxs),
    ],
  );

  @override
  Widget build(BuildContext context) {
    final button = OutlinedButton(
      onPressed: !isLoading ? onPressed : null,
      style: ButtonStyle(
        padding: WidgetStatePropertyAll<EdgeInsets?>(
          innerButtonPadding(buttonSize),
        ),
        foregroundColor: WidgetStateProperty.resolveWith<Color?>(
          (states) => _getForegroundStyle(states, context),
        ),
        iconColor: WidgetStateProperty.resolveWith<Color?>(
          (states) => _getIconStyle(states, context),
        ),
        backgroundColor: WidgetStateProperty.resolveWith<Color?>(
          (states) => _getStyle(states, context),
        ),
        iconSize: const WidgetStatePropertyAll<double?>(xs),
        side: WidgetStatePropertyAll<BorderSide?>(
          BorderSide(color: context.appTheme.colorScheme.outline),
        ),
      ),
      child: _getLabel(context),
    );

    return switch (buttonSize) {
      ButtonSize.responsive => Row(
        children: [
          Expanded(child: button),
        ],
      ),
      _ => button,
    };
  }
}
