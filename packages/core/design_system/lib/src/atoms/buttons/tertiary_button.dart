import 'package:design_system/src/atoms/buttons/base_button.dart';
import 'package:design_system/src/atoms/buttons/button_size.dart';
import 'package:design_system/src/atoms/dimensions.dart';
import 'package:design_system/src/atoms/sizes.dart';
import 'package:design_system/src/theme/theme.dart';
import 'package:flutter/material.dart';

class TertiaryButton extends StatelessWidget {
  const TertiaryButton._({
    required this.title,
    required this.isLoading,
    required this.buttonSize,
    required this.isDeleteButton,
    super.key,
    this.onPressed,
    this.leadingIcon,
    this.trailingIcon,
  });

  factory TertiaryButton.responsive({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
    bool isDeleteButton = false,
  }) => TertiaryButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    isDeleteButton: isDeleteButton,
    buttonSize: ButtonSize.responsive,
  );

  factory TertiaryButton.blocked({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
    bool isDeleteButton = false,
  }) => TertiaryButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    isDeleteButton: isDeleteButton,
    buttonSize: ButtonSize.blocked,
  );

  factory TertiaryButton.small({
    required String title,
    Key? key,
    VoidCallback? onPressed,
    bool isLoading = false,
    Widget? leadingIcon,
    Widget? trailingIcon,
    bool isDeleteButton = false,
  }) => TertiaryButton._(
    key: key,
    onPressed: onPressed,
    title: title,
    isLoading: isLoading,
    leadingIcon: leadingIcon,
    trailingIcon: trailingIcon,
    isDeleteButton: isDeleteButton,
    buttonSize: ButtonSize.small,
  );

  final VoidCallback? onPressed;
  final String title;
  final bool isLoading;
  final ButtonSize buttonSize;
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final bool isDeleteButton;

  Widget _getLabel(BuildContext context) =>
      isLoading ? _getLoadingIndicator(context) : _getLabelWidget();

  Widget _getLoadingIndicator(BuildContext context) => SizedBox.square(
    dimension: sm,
    child: CircularProgressIndicator(
      color: context.appTheme.colorScheme.primary,
    ),
  );

  Color? _getStyle(Set<WidgetState> states, BuildContext context) {
    if (states.contains(WidgetState.disabled)) {
      return isLoading
          ? context.appTheme.colorScheme.surface
          : context.appTheme.disabledColor;
    }
    return null; // Use the component's default.
  }

  Color? _getForegroundStyle(Set<WidgetState> states, BuildContext context) =>
      states.contains(WidgetState.disabled)
      ? context.appTheme.colorScheme.onSecondary.withAlpha(100)
      : context.appTheme.colorScheme.onSecondary;

  Color? _getIconStyle(Set<WidgetState> states, BuildContext context) =>
      states.contains(WidgetState.disabled)
      ? null
      : isDeleteButton
      ? context.appTheme.semanticColorsTheme.darkRed
      : context.appTheme.colorScheme.primary;

  Widget _getLabelWidget() => Row(
    mainAxisAlignment: MainAxisAlignment.center,
    mainAxisSize: MainAxisSize.min,
    children: [
      leadingIcon != null
          ? Padding(
              padding: dimen.left.xxs,
              child: leadingIcon!,
            )
          : const SizedBox(width: xxs),
      Padding(
        padding: dimen.x.xxs,
        child: Text(
          title,
          textAlign: TextAlign.center,
        ),
      ),
      trailingIcon != null
          ? Padding(
              padding: dimen.right.xxs,
              child: trailingIcon!,
            )
          : const SizedBox(width: xxs),
    ],
  );

  @override
  Widget build(BuildContext context) {
    final button = OutlinedButton(
      onPressed: !isLoading ? onPressed : null,
      style: ButtonStyle(
        padding: WidgetStatePropertyAll<EdgeInsets?>(
          innerButtonPadding(buttonSize),
        ),
        foregroundColor: WidgetStateProperty.resolveWith<Color?>(
          (states) => _getForegroundStyle(states, context),
        ),
        iconColor: WidgetStateProperty.resolveWith<Color?>(
          (states) => _getIconStyle(states, context),
        ),
        iconSize: const WidgetStatePropertyAll<double?>(xs),
        backgroundColor: WidgetStateProperty.resolveWith<Color?>(
          (states) => _getStyle(states, context),
        ),
        side: const WidgetStatePropertyAll<BorderSide?>(BorderSide.none),
      ),
      child: _getLabel(context),
    );

    return switch (buttonSize) {
      (ButtonSize.responsive || ButtonSize.bigResponsive) => Row(
        children: [
          Expanded(child: button),
        ],
      ),
      _ => button,
    };
  }
}
