import 'package:design_system/src/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerWithColors extends StatelessWidget {
  const ShimmerWithColors({
    required this.child,
    super.key,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return Shimmer.fromColors(
      baseColor: theme.semanticColorsTheme.greyShades.shade200,
      highlightColor: theme.semanticColorsTheme.greyShades.shade100,
      child: child,
    );
  }
}
