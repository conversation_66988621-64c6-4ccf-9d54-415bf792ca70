import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:meta/meta.dart';

@staticIconProvider
sealed class LMIcons {
  const LMIcons._();

  static const verified = Icons.check_rounded;
  static const add = Icons.add_rounded;
  static const store = Icons.storefront;
  static const storeFilled = Icons.storefront_rounded;
  static const notifications = Icons.notifications_none_rounded;
  static const notificationsFilled = Icons.notifications_rounded;
  static const chatBubble = Icons.chat_bubble_outline;
  static const chatBubbleFilled = Icons.chat_bubble_rounded;
  static const profile = Icons.person_outline_rounded;
  static const profileFilled = Icons.person_rounded;
  static const favorite = Icons.favorite_border_rounded;
  static const favoriteFilled = Icons.favorite_rounded;
  static const star = Icons.star_border_rounded;
  static const starFilled = Icons.star_rounded;
  static const calendar = Icons.calendar_today_rounded;
  static const chevronRight = Icons.chevron_right_rounded;
  static const chevronLeft = Icons.chevron_left_rounded;

  // Free FontAwesome (Only used in)
  @internal
  static const fa = _FaIcons();
}

class _FaIcons {
  const _FaIcons();

  final atom = FontAwesomeIcons.atom;
  final molecule = FontAwesomeIcons.dna;
  final organism = FontAwesomeIcons.bacteria;
  final template = FontAwesomeIcons.copy;
}
