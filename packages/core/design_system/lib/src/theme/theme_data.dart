part of 'theme.dart';

extension BuildContextX on BuildContext {
  ThemeData get appTheme => Theme.of(this);
}

extension ThemeDataCommonExtension on ThemeData {
  ThemeData get common => copyWith(
    filledButtonTheme: _CommonAppThemeData.filledButtonThemeData,
    outlinedButtonTheme: _CommonAppThemeData.outlinedButtonThemeData,
    inputDecorationTheme: _CommonAppThemeData.inputDecorationTheme,
  );
}

class _CommonAppThemeData {
  static final filledButtonThemeData = FilledButtonThemeData(
    style: FilledButton.styleFrom(
      padding: dimen.x.xs,
      shape: const StadiumBorder(),
    ),
  );

  static final outlinedButtonThemeData = OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      padding: dimen.x.xs,
      shape: const StadiumBorder(),
    ),
  );

  static final inputDecorationTheme = InputDecorationTheme(
    filled: true,
    fillColor: _AppColors.white,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(xxs),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(xxs),
      borderSide: const BorderSide(color: _AppColors.grey200),
    ),
  );
}
