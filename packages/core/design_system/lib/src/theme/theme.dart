import 'package:design_system/src/atoms/dimensions.dart';
import 'package:design_system/src/atoms/sizes.dart';
import 'package:design_system/src/theme/utils/annotations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';

part 'app_colors.dart';
part 'color_scheme.dart';
part 'theme_data.dart';
part 'extensions/theme_extensions.dart';
part 'extensions/text_theme_extensions.dart';
part 'extensions/elevation_extensions.dart';
part 'theme.tailor.dart';

class AppTheme {
  static ThemeData get light => ThemeData().common.copyWith(
    brightness: Brightness.light,
    colorScheme: _AppColorsScheme.light,
    splashColor: _AppColorsScheme.light.secondary,
    highlightColor: _AppColorsScheme.light.primary.withAlpha(25),
    dividerColor: _AppColorsScheme.light.outline,
    dividerTheme: DividerThemeData(color: _AppColorsScheme.light.outline),
    scaffoldBackgroundColor: _AppColorsScheme.light.surface,
    snackBarTheme: SnackBarThemeData(
      contentTextStyle: TextStyle(color: _AppColorsScheme.light.surface),
    ),
    appBarTheme: AppBarTheme(
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarBrightness: Brightness.light,
        statusBarColor: _AppColorsScheme.light.onSurface,
      ),
    ),
    extensions: [
      _lightSemanticColors,
      AppTextTheme.main(_AppColorsScheme.light.onSurface),
      AppElevations.main(
        _AppColorsScheme.light.primary,
        _lightSemanticColors.greyShades.shade900,
      ),
    ],
  );

  static ThemeData get dark => ThemeData().common.copyWith(
    brightness: Brightness.dark,
    colorScheme: _AppColorsScheme.dark,
    splashColor: _AppColorsScheme.dark.secondary,
    highlightColor: _AppColorsScheme.dark.primary.withAlpha(25),
    dividerColor: _AppColorsScheme.dark.outline,
    dividerTheme: DividerThemeData(color: _AppColorsScheme.dark.outline),
    scaffoldBackgroundColor: _AppColorsScheme.dark.surface,
    snackBarTheme: SnackBarThemeData(
      contentTextStyle: TextStyle(color: _AppColorsScheme.dark.surface),
    ),
    appBarTheme: AppBarTheme(
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarBrightness: Brightness.dark,
        statusBarColor: _AppColorsScheme.dark.onSurface,
      ),
    ),
    extensions: [
      _darkSemanticColors,
      AppTextTheme.main(_AppColorsScheme.dark.onSurface),
      AppElevations.main(
        _AppColorsScheme.dark.primary,
        _darkSemanticColors.greyShades.shade900,
      ),
    ],
  );
}
