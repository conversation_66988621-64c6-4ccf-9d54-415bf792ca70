part of 'theme.dart';

class _AppColorsScheme {
  static ColorScheme get light => const ColorScheme.light(
    primary: _AppColors.primary,
    primaryFixed: _AppColors.primary400,
    primaryFixedDim: _AppColors.primary700,
    secondary: _AppColors.primary100,
    error: _AppColors.red700,
    onPrimary: _AppColors.grey100,
    onSecondary: _AppColors.grey900,
    outline: _AppColors.grey,
    onSurface: _AppColors.grey900,
    surface: _AppColors.grey100,
    surfaceTint: _AppColors.white,
  );

  /// TODO(Tryneeth): Se actualizará en cuanto se tenga el esquema de colores
  /// para el tema oscuro
  static ColorScheme get dark => light;
}
