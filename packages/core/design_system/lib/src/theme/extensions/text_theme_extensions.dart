part of '../theme.dart';

@lunchManagerTailorMixin
class AppTextTheme extends ThemeExtension<AppTextTheme>
    with _$AppTextThemeTailorMixin {
  const AppTextTheme({
    required this.display1,
    required this.display2,
    required this.display3,
    required this.display4,
    required this.headline1,
    required this.headline2,
    required this.headline3,
    required this.headline4,
    required this.headline5,
    required this.headline6,
    required this.body1,
    required this.body2,
    required this.body3,
    required this.label1,
    required this.label2,
    required this.label3,
  });

  factory AppTextTheme.main(Color textColor) => AppTextTheme(
    display1: TextStyle(
      fontSize: 64,
      fontWeight: FontWeight.w500,
      color: textColor,
      letterSpacing: -0.25,
      height: 1,
    ),
    display2: TextStyle(
      fontSize: 56,
      fontWeight: FontWeight.w400,
      color: textColor,
      letterSpacing: -0.25,
      height: 64 / 56,
    ),
    display3: TextStyle(
      fontSize: 44,
      fontWeight: FontWeight.w700,
      color: textColor,
      letterSpacing: 1,
      height: 52 / 44,
    ),
    display4: TextStyle(
      fontSize: 36,
      fontWeight: FontWeight.w400,
      color: textColor,
      letterSpacing: 1,
      height: 44 / 36,
    ),
    headline1: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.w400,
      color: textColor,
      letterSpacing: 1,
      height: 40 / 32,
    ),
    headline2: TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.w500,
      color: textColor,
      letterSpacing: 0,
      height: 36 / 28,
    ),
    headline3: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w400,
      color: textColor,
      letterSpacing: 0.15,
      height: 32 / 24,
    ),
    headline4: TextStyle(
      fontSize: 22,
      fontWeight: FontWeight.w600,
      color: textColor,
      letterSpacing: 0,
      height: 28 / 22,
    ),
    headline5: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: textColor,
      letterSpacing: 0,
      height: 24 / 16,
    ),
    headline6: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: textColor,
      letterSpacing: 0.1,
      height: 20 / 14,
    ),
    body1: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: textColor,
      letterSpacing: 0,
      height: 1,
    ),
    body2: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w800,
      color: textColor,
      letterSpacing: 12 * 0.04,
      height: 16 / 12,
    ),
    body3: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: textColor,
      letterSpacing: 0,
      height: 1,
    ),
    label1: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: textColor,
      letterSpacing: 0,
      height: 16 / 14,
    ),
    label2: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w300,
      color: textColor,
      letterSpacing: 0,
      height: 16 / 14,
    ),
    label3: TextStyle(
      fontSize: 11,
      fontWeight: FontWeight.w600,
      color: textColor,
      letterSpacing: 0,
      height: 12 / 11,
    ),
  );

  final TextStyle display1;
  final TextStyle display2;
  final TextStyle display3;
  final TextStyle display4;
  final TextStyle headline1;
  final TextStyle headline2;
  final TextStyle headline3;
  final TextStyle headline4;
  final TextStyle headline5;
  final TextStyle headline6;
  final TextStyle body1;
  final TextStyle body2;
  final TextStyle body3;
  final TextStyle label1;
  final TextStyle label2;
  final TextStyle label3;
}
