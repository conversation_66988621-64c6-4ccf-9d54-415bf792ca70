part of '../theme.dart';

@lunchManagerTailorMixin
class SemanticColorsTheme extends ThemeExtension<SemanticColorsTheme>
    with _$SemanticColorsThemeTailorMixin {
  const SemanticColorsTheme({
    required this.red,
    required this.lightRed,
    required this.darkRed,
    required this.yellow,
    required this.lightYellow,
    required this.darkYellow,
    required this.green,
    required this.lightGreen,
    required this.mediumGreen,
    required this.grey,
    required this.greyShades,
  });

  final Color red;
  final Color lightRed;
  final Color darkRed;

  final Color yellow;
  final Color lightYellow;
  final Color darkYellow;

  final Color green;
  final Color lightGreen;
  final Color mediumGreen;

  final Color grey;
  final _SemanticGrey greyShades;
}

@TailorMixinComponent()
class _SemanticGrey extends ThemeExtension<_SemanticGrey>
    with _$_SemanticGreyTailorMixin {
  const _SemanticGrey({
    required this.shade100,
    required this.shade150,
    required this.shade200,
    required this.shade500,
    required this.shade600,
    required this.shade800,
    required this.shade900,
  });

  final Color shade100;
  final Color shade150;
  final Color shade200;
  final Color shade500;
  final Color shade600;
  final Color shade800;
  final Color shade900;
}

const _lightSemanticColors = SemanticColorsTheme(
  red: _AppColors.red,
  lightRed: _AppColors.red100,
  darkRed: _AppColors.red700,
  yellow: _AppColors.yellow,
  lightYellow: _AppColors.yellow100,
  darkYellow: _AppColors.yellow700,
  green: _AppColors.green,
  lightGreen: _AppColors.green200,
  mediumGreen: _AppColors.green400,
  grey: _AppColors.grey,
  greyShades: _SemanticGrey(
    shade100: _AppColors.grey100,
    shade150: _AppColors.grey150,
    shade200: _AppColors.grey200,
    shade500: _AppColors.grey,
    shade600: _AppColors.grey600,
    shade800: _AppColors.grey800,
    shade900: _AppColors.grey900,
  ),
);

/// TODO(Tryneeth): Se actualizará en cuanto se tenga el esquema de colores
/// para el tema oscuro
const _darkSemanticColors = _lightSemanticColors;
