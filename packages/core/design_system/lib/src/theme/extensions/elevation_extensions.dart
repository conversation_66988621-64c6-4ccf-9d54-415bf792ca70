part of '../theme.dart';

@lunchManagerTailorMixin
class AppElevations extends ThemeExtension<AppElevations>
    with _$AppElevationsTailorMixin {
  AppElevations({
    required this.elevation1,
    required this.elevation2,
    required this.elevation3,
  });

  factory AppElevations.main(
    Color primaryShadow,
    Color secondaryShadow,
  ) {
    return AppElevations(
      elevation1: [
        BoxShadow(
          color: secondaryShadow.withAlpha(12),
          blurRadius: 2.5,
          offset: const Offset(none, xxsPlus),
        ),
        BoxShadow(
          color: secondaryShadow.withAlpha(25),
          blurRadius: 7.6,
          offset: const Offset(none, micro),
        ),
      ],
      elevation2: [
        BoxShadow(
          color: primaryShadow.withAlpha(50),
          blurRadius: 9.6,
          offset: const Offset(none, xxsPlus),
        ),
        BoxShadow(
          color: primaryShadow.withAlpha(25),
          blurRadius: 7.6,
          offset: const Offset(none, micro),
        ),
      ],
      elevation3: [
        BoxShadow(
          color: primaryShadow.withAlpha(75),
          blurRadius: 9.6,
          offset: const Offset(none, xs),
          spreadRadius: -xxs,
        ),
        BoxShadow(
          color: primaryShadow.withAlpha(25),
          blurRadius: 7.6,
          offset: const Offset(none, micro),
        ),
      ],
    );
  }

  final List<BoxShadow> elevation1;
  final List<BoxShadow> elevation2;
  final List<BoxShadow> elevation3;
}

extension BoxShadowListX on List<BoxShadow> {
  List<BoxShadow> withouOffset() => this
      .map(
        (e) => BoxShadow(
          color: e.color,
          blurRadius: e.blurRadius,
          spreadRadius: e.spreadRadius,
        ),
      )
      .toList();
}
