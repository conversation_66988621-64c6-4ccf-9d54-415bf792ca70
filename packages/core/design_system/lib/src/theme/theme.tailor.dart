// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint, unused_element, unnecessary_cast

part of 'theme.dart';

// **************************************************************************
// TailorAnnotationsGenerator
// **************************************************************************

mixin _$SemanticColorsThemeTailorMixin on ThemeExtension<SemanticColorsTheme> {
  Color get red;
  Color get lightRed;
  Color get darkRed;
  Color get yellow;
  Color get lightYellow;
  Color get darkYellow;
  Color get green;
  Color get lightGreen;
  Color get mediumGreen;
  Color get grey;
  _SemanticGrey get greyShades;

  @override
  SemanticColorsTheme copyWith({
    Color? red,
    Color? lightRed,
    Color? darkRed,
    Color? yellow,
    Color? lightYellow,
    Color? darkYellow,
    Color? green,
    Color? lightGreen,
    Color? mediumGreen,
    Color? grey,
    _SemanticGrey? greyShades,
  }) {
    return SemanticColorsTheme(
      red: red ?? this.red,
      lightRed: lightRed ?? this.lightRed,
      darkRed: darkRed ?? this.darkRed,
      yellow: yellow ?? this.yellow,
      lightYellow: lightYellow ?? this.lightYellow,
      darkYellow: darkYellow ?? this.darkYellow,
      green: green ?? this.green,
      lightGreen: lightGreen ?? this.lightGreen,
      mediumGreen: mediumGreen ?? this.mediumGreen,
      grey: grey ?? this.grey,
      greyShades: greyShades ?? this.greyShades,
    );
  }

  @override
  SemanticColorsTheme lerp(
    covariant ThemeExtension<SemanticColorsTheme>? other,
    double t,
  ) {
    if (other is! SemanticColorsTheme) return this as SemanticColorsTheme;
    return SemanticColorsTheme(
      red: Color.lerp(red, other.red, t)!,
      lightRed: Color.lerp(lightRed, other.lightRed, t)!,
      darkRed: Color.lerp(darkRed, other.darkRed, t)!,
      yellow: Color.lerp(yellow, other.yellow, t)!,
      lightYellow: Color.lerp(lightYellow, other.lightYellow, t)!,
      darkYellow: Color.lerp(darkYellow, other.darkYellow, t)!,
      green: Color.lerp(green, other.green, t)!,
      lightGreen: Color.lerp(lightGreen, other.lightGreen, t)!,
      mediumGreen: Color.lerp(mediumGreen, other.mediumGreen, t)!,
      grey: Color.lerp(grey, other.grey, t)!,
      greyShades: greyShades.lerp(other.greyShades, t) as _SemanticGrey,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SemanticColorsTheme &&
            const DeepCollectionEquality().equals(red, other.red) &&
            const DeepCollectionEquality().equals(lightRed, other.lightRed) &&
            const DeepCollectionEquality().equals(darkRed, other.darkRed) &&
            const DeepCollectionEquality().equals(yellow, other.yellow) &&
            const DeepCollectionEquality().equals(
              lightYellow,
              other.lightYellow,
            ) &&
            const DeepCollectionEquality().equals(
              darkYellow,
              other.darkYellow,
            ) &&
            const DeepCollectionEquality().equals(green, other.green) &&
            const DeepCollectionEquality().equals(
              lightGreen,
              other.lightGreen,
            ) &&
            const DeepCollectionEquality().equals(
              mediumGreen,
              other.mediumGreen,
            ) &&
            const DeepCollectionEquality().equals(grey, other.grey) &&
            const DeepCollectionEquality().equals(
              greyShades,
              other.greyShades,
            ));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(red),
      const DeepCollectionEquality().hash(lightRed),
      const DeepCollectionEquality().hash(darkRed),
      const DeepCollectionEquality().hash(yellow),
      const DeepCollectionEquality().hash(lightYellow),
      const DeepCollectionEquality().hash(darkYellow),
      const DeepCollectionEquality().hash(green),
      const DeepCollectionEquality().hash(lightGreen),
      const DeepCollectionEquality().hash(mediumGreen),
      const DeepCollectionEquality().hash(grey),
      const DeepCollectionEquality().hash(greyShades),
    );
  }
}

extension SemanticColorsThemeThemeData on ThemeData {
  SemanticColorsTheme get semanticColorsTheme =>
      extension<SemanticColorsTheme>()!;
}

mixin _$_SemanticGreyTailorMixin on ThemeExtension<_SemanticGrey> {
  Color get shade100;
  Color get shade150;
  Color get shade200;
  Color get shade500;
  Color get shade600;
  Color get shade800;
  Color get shade900;

  @override
  _SemanticGrey copyWith({
    Color? shade100,
    Color? shade150,
    Color? shade200,
    Color? shade500,
    Color? shade600,
    Color? shade800,
    Color? shade900,
  }) {
    return _SemanticGrey(
      shade100: shade100 ?? this.shade100,
      shade150: shade150 ?? this.shade150,
      shade200: shade200 ?? this.shade200,
      shade500: shade500 ?? this.shade500,
      shade600: shade600 ?? this.shade600,
      shade800: shade800 ?? this.shade800,
      shade900: shade900 ?? this.shade900,
    );
  }

  @override
  _SemanticGrey lerp(covariant ThemeExtension<_SemanticGrey>? other, double t) {
    if (other is! _SemanticGrey) return this as _SemanticGrey;
    return _SemanticGrey(
      shade100: Color.lerp(shade100, other.shade100, t)!,
      shade150: Color.lerp(shade150, other.shade150, t)!,
      shade200: Color.lerp(shade200, other.shade200, t)!,
      shade500: Color.lerp(shade500, other.shade500, t)!,
      shade600: Color.lerp(shade600, other.shade600, t)!,
      shade800: Color.lerp(shade800, other.shade800, t)!,
      shade900: Color.lerp(shade900, other.shade900, t)!,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SemanticGrey &&
            const DeepCollectionEquality().equals(shade100, other.shade100) &&
            const DeepCollectionEquality().equals(shade150, other.shade150) &&
            const DeepCollectionEquality().equals(shade200, other.shade200) &&
            const DeepCollectionEquality().equals(shade500, other.shade500) &&
            const DeepCollectionEquality().equals(shade600, other.shade600) &&
            const DeepCollectionEquality().equals(shade800, other.shade800) &&
            const DeepCollectionEquality().equals(shade900, other.shade900));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(shade100),
      const DeepCollectionEquality().hash(shade150),
      const DeepCollectionEquality().hash(shade200),
      const DeepCollectionEquality().hash(shade500),
      const DeepCollectionEquality().hash(shade600),
      const DeepCollectionEquality().hash(shade800),
      const DeepCollectionEquality().hash(shade900),
    );
  }
}

mixin _$AppTextThemeTailorMixin on ThemeExtension<AppTextTheme> {
  TextStyle get display1;
  TextStyle get display2;
  TextStyle get display3;
  TextStyle get display4;
  TextStyle get headline1;
  TextStyle get headline2;
  TextStyle get headline3;
  TextStyle get headline4;
  TextStyle get headline5;
  TextStyle get headline6;
  TextStyle get body1;
  TextStyle get body2;
  TextStyle get body3;
  TextStyle get label1;
  TextStyle get label2;
  TextStyle get label3;

  @override
  AppTextTheme copyWith({
    TextStyle? display1,
    TextStyle? display2,
    TextStyle? display3,
    TextStyle? display4,
    TextStyle? headline1,
    TextStyle? headline2,
    TextStyle? headline3,
    TextStyle? headline4,
    TextStyle? headline5,
    TextStyle? headline6,
    TextStyle? body1,
    TextStyle? body2,
    TextStyle? body3,
    TextStyle? label1,
    TextStyle? label2,
    TextStyle? label3,
  }) {
    return AppTextTheme(
      display1: display1 ?? this.display1,
      display2: display2 ?? this.display2,
      display3: display3 ?? this.display3,
      display4: display4 ?? this.display4,
      headline1: headline1 ?? this.headline1,
      headline2: headline2 ?? this.headline2,
      headline3: headline3 ?? this.headline3,
      headline4: headline4 ?? this.headline4,
      headline5: headline5 ?? this.headline5,
      headline6: headline6 ?? this.headline6,
      body1: body1 ?? this.body1,
      body2: body2 ?? this.body2,
      body3: body3 ?? this.body3,
      label1: label1 ?? this.label1,
      label2: label2 ?? this.label2,
      label3: label3 ?? this.label3,
    );
  }

  @override
  AppTextTheme lerp(covariant ThemeExtension<AppTextTheme>? other, double t) {
    if (other is! AppTextTheme) return this as AppTextTheme;
    return AppTextTheme(
      display1: TextStyle.lerp(display1, other.display1, t)!,
      display2: TextStyle.lerp(display2, other.display2, t)!,
      display3: TextStyle.lerp(display3, other.display3, t)!,
      display4: TextStyle.lerp(display4, other.display4, t)!,
      headline1: TextStyle.lerp(headline1, other.headline1, t)!,
      headline2: TextStyle.lerp(headline2, other.headline2, t)!,
      headline3: TextStyle.lerp(headline3, other.headline3, t)!,
      headline4: TextStyle.lerp(headline4, other.headline4, t)!,
      headline5: TextStyle.lerp(headline5, other.headline5, t)!,
      headline6: TextStyle.lerp(headline6, other.headline6, t)!,
      body1: TextStyle.lerp(body1, other.body1, t)!,
      body2: TextStyle.lerp(body2, other.body2, t)!,
      body3: TextStyle.lerp(body3, other.body3, t)!,
      label1: TextStyle.lerp(label1, other.label1, t)!,
      label2: TextStyle.lerp(label2, other.label2, t)!,
      label3: TextStyle.lerp(label3, other.label3, t)!,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppTextTheme &&
            const DeepCollectionEquality().equals(display1, other.display1) &&
            const DeepCollectionEquality().equals(display2, other.display2) &&
            const DeepCollectionEquality().equals(display3, other.display3) &&
            const DeepCollectionEquality().equals(display4, other.display4) &&
            const DeepCollectionEquality().equals(headline1, other.headline1) &&
            const DeepCollectionEquality().equals(headline2, other.headline2) &&
            const DeepCollectionEquality().equals(headline3, other.headline3) &&
            const DeepCollectionEquality().equals(headline4, other.headline4) &&
            const DeepCollectionEquality().equals(headline5, other.headline5) &&
            const DeepCollectionEquality().equals(headline6, other.headline6) &&
            const DeepCollectionEquality().equals(body1, other.body1) &&
            const DeepCollectionEquality().equals(body2, other.body2) &&
            const DeepCollectionEquality().equals(body3, other.body3) &&
            const DeepCollectionEquality().equals(label1, other.label1) &&
            const DeepCollectionEquality().equals(label2, other.label2) &&
            const DeepCollectionEquality().equals(label3, other.label3));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(display1),
      const DeepCollectionEquality().hash(display2),
      const DeepCollectionEquality().hash(display3),
      const DeepCollectionEquality().hash(display4),
      const DeepCollectionEquality().hash(headline1),
      const DeepCollectionEquality().hash(headline2),
      const DeepCollectionEquality().hash(headline3),
      const DeepCollectionEquality().hash(headline4),
      const DeepCollectionEquality().hash(headline5),
      const DeepCollectionEquality().hash(headline6),
      const DeepCollectionEquality().hash(body1),
      const DeepCollectionEquality().hash(body2),
      const DeepCollectionEquality().hash(body3),
      const DeepCollectionEquality().hash(label1),
      const DeepCollectionEquality().hash(label2),
      const DeepCollectionEquality().hash(label3),
    );
  }
}

extension AppTextThemeThemeData on ThemeData {
  AppTextTheme get appTextTheme => extension<AppTextTheme>()!;
}

mixin _$AppElevationsTailorMixin on ThemeExtension<AppElevations> {
  List<BoxShadow> get elevation1;
  List<BoxShadow> get elevation2;
  List<BoxShadow> get elevation3;

  @override
  AppElevations copyWith({
    List<BoxShadow>? elevation1,
    List<BoxShadow>? elevation2,
    List<BoxShadow>? elevation3,
  }) {
    return AppElevations(
      elevation1: elevation1 ?? this.elevation1,
      elevation2: elevation2 ?? this.elevation2,
      elevation3: elevation3 ?? this.elevation3,
    );
  }

  @override
  AppElevations lerp(covariant ThemeExtension<AppElevations>? other, double t) {
    if (other is! AppElevations) return this as AppElevations;
    return AppElevations(
      elevation1: t < 0.5 ? elevation1 : other.elevation1,
      elevation2: t < 0.5 ? elevation2 : other.elevation2,
      elevation3: t < 0.5 ? elevation3 : other.elevation3,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AppElevations &&
            const DeepCollectionEquality().equals(
              elevation1,
              other.elevation1,
            ) &&
            const DeepCollectionEquality().equals(
              elevation2,
              other.elevation2,
            ) &&
            const DeepCollectionEquality().equals(
              elevation3,
              other.elevation3,
            ));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(elevation1),
      const DeepCollectionEquality().hash(elevation2),
      const DeepCollectionEquality().hash(elevation3),
    );
  }
}

extension AppElevationsThemeData on ThemeData {
  AppElevations get appElevations => extension<AppElevations>()!;
}
