import 'package:design_system/src/atoms/dimensions.dart';
import 'package:design_system/src/atoms/icons/lunch_manager_icons.dart';
import 'package:design_system/src/atoms/sizes.dart';
import 'package:design_system/src/theme/theme.dart';
import 'package:flutter/material.dart';

class AvatarListTile extends StatelessWidget {
  const AvatarListTile({
    required this.image,
    required this.title,
    required this.description,
    required this.onTap,
    super.key,
  });

  final ImageProvider<Object> image;
  final String title;
  final String description;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    final border = Border.all(
      color: theme.semanticColorsTheme.greyShades.shade200,
    );
    const borderRadius = BorderRadius.all(Radius.circular(xxs));
    return Ink(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceTint,
        boxShadow: theme.appElevations.elevation1,
        border: border,
        borderRadius: borderRadius,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: Padding(
          padding: dimen.all.xxs,
          child: Row(
            children: [
              _Avatar(border: border, image: image),
              const SizedBox(width: xxs),
              Expanded(
                child: _Texts(
                  title: title,
                  theme: theme,
                  description: description,
                ),
              ),
              const SizedBox(width: micro),
              const SizedBox.square(
                dimension: md,
                child: Icon(
                  LMIcons.chevronRight,
                  size: xs,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _Texts extends StatelessWidget {
  const _Texts({
    required this.title,
    required this.theme,
    required this.description,
  });

  final String title;
  final ThemeData theme;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: theme.appTextTheme.headline5),
        Text(
          description,
          style: theme.appTextTheme.body3.copyWith(
            color: theme.semanticColorsTheme.greyShades.shade600,
          ),
        ),
      ],
    );
  }
}

class _Avatar extends StatelessWidget {
  const _Avatar({
    required this.border,
    required this.image,
  });

  final Border border;
  final ImageProvider<Object> image;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: xl,
      height: xl,
      decoration: BoxDecoration(
        border: border,
        image: DecorationImage(
          image: image,
          fit: BoxFit.cover,
        ),
        shape: BoxShape.circle,
      ),
    );
  }
}
