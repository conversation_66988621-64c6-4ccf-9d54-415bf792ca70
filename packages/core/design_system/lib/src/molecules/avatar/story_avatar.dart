import 'package:design_system/src/atoms/dimensions.dart';
import 'package:design_system/src/atoms/empty_widgets.dart';
import 'package:design_system/src/atoms/icons/lunch_manager_icons.dart';
import 'package:design_system/src/atoms/shimmer/shimmer_with_colors.dart';
import 'package:design_system/src/atoms/sizes.dart';
import 'package:design_system/src/molecules/avatar/story_avatar_status.dart';
import 'package:design_system/src/theme/theme.dart';
import 'package:flutter/material.dart';

const _kBaseDecoration = BoxDecoration(shape: BoxShape.circle);

class StoryAvatar extends StatelessWidget {
  const StoryAvatar({
    required this.image,
    required this.onTap,
    super.key,
    this.label,
    this.hasNewStories = false,
    this.status = StoryAvatarStatus.offline,
  });

  factory StoryAvatar.add({
    required ImageProvider<Object> image,
    VoidCallback? onTap,
    bool? hasNewStories,
    String? label,
  }) => StoryAvatar(
    image: image,
    onTap: onTap,
    label: label,
    hasNewStories: hasNewStories ?? false,
    status: StoryAvatarStatus.add,
  );

  static _LoadingContent loading() => const _LoadingContent();

  final ImageProvider<Object> image;
  final VoidCallback? onTap;
  final StoryAvatarStatus status;
  final bool hasNewStories;
  final String? label;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Center(
            child: SizedBox.square(
              dimension: xxxl,
              child: Stack(
                children: [
                  _CircledAvatar(
                    image: image,
                    status: status,
                    hasNewStories: hasNewStories,
                  ),
                  Positioned(
                    right: none,
                    bottom: none,
                    child: _CircledStatus(
                      status: status,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: xxs),
          if (label != null)
            SizedBox(
              width: xxxl,
              height: xxsPlus,
              child: Text(
                label!,
                softWrap: true,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: context.appTheme.appTextTheme.label3,
              ),
            ),
        ],
      ),
    );
  }
}

class _CircledAvatar extends StatelessWidget {
  const _CircledAvatar({
    required this.image,
    required this.status,
    this.hasNewStories = false,
  });

  final ImageProvider<Object> image;
  final bool hasNewStories;
  final StoryAvatarStatus status;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;

    final borderColor = switch ((hasNewStories, status)) {
      (false, StoryAvatarStatus.add) => theme.colorScheme.surface,
      (false, _) => theme.semanticColorsTheme.greyShades.shade200,
      (_, _) => theme.colorScheme.primary,
    };

    return Ink(
      decoration: _kBaseDecoration.copyWith(
        color: theme.colorScheme.surface,
        border: Border.all(
          color: borderColor,
          width: nano,
        ),
        boxShadow: status == StoryAvatarStatus.add
            ? theme.appElevations.elevation2
            : null,
      ),
      child: Padding(
        padding: dimen.all.nano,
        child: Ink(
          decoration: _kBaseDecoration.copyWith(
            image: DecorationImage(fit: BoxFit.cover, image: image),
          ),
        ),
      ),
    );
  }
}

class _CircledStatus extends StatelessWidget {
  const _CircledStatus({
    required this.status,
  });

  final StoryAvatarStatus status;

  (IconData?, Color) _getStyle(BuildContext context) {
    final theme = context.appTheme;

    return switch (status) {
      StoryAvatarStatus.onlineVerified => (
        LMIcons.verified,
        theme.semanticColorsTheme.green,
      ),
      StoryAvatarStatus.offlineVerified => (
        LMIcons.verified,
        theme.colorScheme.primary,
      ),
      StoryAvatarStatus.online => (
        null,
        theme.semanticColorsTheme.green,
      ),
      StoryAvatarStatus.offline => (
        null,
        theme.colorScheme.primary,
      ),
      StoryAvatarStatus.add => (
        LMIcons.add,
        theme.colorScheme.primary,
      ),
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    final (iconData, color) = _getStyle(context);

    return status == StoryAvatarStatus.offline
        ? emptyWidget
        : Ink(
            width: sm,
            height: sm,
            padding: dimen.all.none,
            decoration: _kBaseDecoration.copyWith(
              border: Border.all(
                color: theme.semanticColorsTheme.greyShades.shade100,
                width: nano,
              ),
              color: color,
            ),
            child: Center(
              child: Icon(
                iconData,
                size: xs,
                color: theme.colorScheme.surface,
              ),
            ),
          );
  }
}

class _LoadingContent extends StatelessWidget {
  const _LoadingContent();

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Center(
          child: SizedBox.square(
            dimension: xxxl,
            child: Stack(
              children: [
                Container(
                  decoration: _kBaseDecoration.copyWith(
                    color: theme.colorScheme.surface,
                    border: Border.all(
                      color: theme.colorScheme.surface,
                      width: nano,
                    ),
                  ),
                  child: Padding(
                    padding: dimen.all.nano,
                    child: ShimmerWithColors(
                      child: Container(
                        decoration: _kBaseDecoration.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          width: xxxl,
          height: xxsPlus,
          child: Text(
            '',
            softWrap: true,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            style: context.appTheme.appTextTheme.label3,
          ),
        ),
      ],
    );
  }
}
