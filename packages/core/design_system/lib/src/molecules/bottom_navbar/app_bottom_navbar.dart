import 'package:design_system/src/atoms/dimensions.dart';
import 'package:design_system/src/atoms/sizes.dart';
import 'package:design_system/src/molecules/bottom_navbar/app_bottom_navbar_item.dart';
import 'package:design_system/src/theme/theme.dart';
import 'package:flutter/material.dart';

class AppBottomNavbar extends StatelessWidget {
  const AppBottomNavbar({
    required this.currentIndex,
    required this.items,
    super.key,
    this.onTap,
  });

  final int currentIndex;
  final Function(int)? onTap;
  final List<AppBottomNavbarItem> items;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return DecoratedBox(
      decoration: BoxDecoration(
        boxShadow: theme.appElevations.elevation2.withouOffset(),
      ),
      child: BottomNavigationBar(
        onTap: onTap,
        currentIndex: currentIndex,
        type: BottomNavigationBarType.fixed,
        showUnselectedLabels: true,
        useLegacyColorScheme: false,
        landscapeLayout: BottomNavigationBarLandscapeLayout.centered,
        backgroundColor: theme.colorScheme.surfaceTint,
        selectedItemColor: theme.colorScheme.primary,
        selectedLabelStyle: theme.appTextTheme.label3,
        unselectedLabelStyle: theme.appTextTheme.label3.copyWith(
          color: theme.semanticColorsTheme.grey,
        ),
        iconSize: sm + micro,
        items: [...items.map((e) => e.toBottomNavbarItem())],
      ),
    );
  }
}

class _Badge extends StatelessWidget {
  const _Badge();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: xs,
      width: xs,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: context.appTheme.colorScheme.primary,
        border: Border.all(
          width: nano,
          color: context.appTheme.colorScheme.surface,
        ),
      ),
    );
  }
}

extension on AppBottomNavbarItem {
  BottomNavigationBarItem toBottomNavbarItem() {
    return BottomNavigationBarItem(
      label: label,
      icon: Stack(
        clipBehavior: Clip.none,
        children: [
          Stack(
            children: [
              Padding(
                padding: dimen.all.nano,
                child: icon,
              ),
              if (showBadge)
                const Positioned(
                  top: none,
                  right: none,
                  child: _Badge(),
                ),
            ],
          ),
        ],
      ),
      activeIcon: Padding(
        padding: dimen.bottom.nano,
        child: activeIcon,
      ),
    );
  }
}
