import 'package:flutter/material.dart';

class AppBottomNavbarItem {
  const AppBottomNavbarItem({
    required this.icon,
    required this.label,
    this.key,
    Icon? activeIcon,
    this.showBadge = false,
    this.badgeCount = 0,
  }) : activeIcon = activeIcon ?? icon;

  final Key? key;
  final Icon icon;
  final Icon? activeIcon;
  final String label;
  final bool showBadge;
  final int badgeCount;
}
