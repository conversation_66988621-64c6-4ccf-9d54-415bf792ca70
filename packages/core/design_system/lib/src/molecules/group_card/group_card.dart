import 'package:design_system/design_system.dart';
import 'package:design_system/src/molecules/label/group_status_label.dart';
import 'package:flutter/material.dart';

class GroupCard extends StatelessWidget {
  const GroupCard({
    required this.image,
    required this.title,
    required this.description,
    super.key,
    this.onTap,
    this.onToggleFavorite,
    this.isActive = false,
    this.isFavorite = false,
    this.statusLabel,
    this.status = GroupCardStatus.unlocked,
  }) : _isSmall = false;

  const GroupCard.small({
    required this.image,
    required this.title,
    this.onTap,
    this.status = GroupCardStatus.unlocked,
    this.isActive = false,
  }) : _isSmall = true,
       isFavorite = true,
       onToggleFavorite = null,
       description = '',
       statusLabel = '';

  final String title;
  final String description;
  final ImageProvider<Object> image;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onToggleFavorite;
  final bool isActive;
  final bool isFavorite;
  final GroupCardStatus status;
  final String? statusLabel;
  final bool _isSmall;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    final textColor = theme.semanticColorsTheme.greyShades.shade100;

    final decoration = BoxDecoration(
      color: theme.colorScheme.surface,
      borderRadius: BorderRadius.circular(xxs),
      image: DecorationImage(image: image, fit: BoxFit.cover),
      boxShadow: isActive && isFavorite ? theme.appElevations.elevation3 : null,
    );

    return GestureDetector(
      onTap: onTap,
      child: Center(
        child: _isSmall
            ? _SmallGroupCard(
                decoration: decoration,
                isActive: isActive,
                title: title,
                status: status,
              )
            : _RegularGroupCard(
                decoration: decoration,
                isActive: isActive,
                status: status,
                statusLabel: statusLabel,
                isFavorite: isFavorite,
                textColor: textColor,
                title: title,
                description: description,
                onToggleFavorite: onToggleFavorite,
              ),
      ),
    );
  }
}

class _RegularGroupCard extends StatelessWidget {
  const _RegularGroupCard({
    required this.decoration,
    required this.isActive,
    required this.status,
    required this.statusLabel,
    required this.isFavorite,
    required this.textColor,
    required this.title,
    required this.description,
    this.onToggleFavorite,
  });

  final BoxDecoration decoration;
  final bool isActive;
  final GroupCardStatus status;
  final String? statusLabel;
  final bool isFavorite;
  final Color textColor;
  final String title;
  final String description;
  final ValueChanged<bool>? onToggleFavorite;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return Ink(
      width: 152,
      height: 200,
      decoration: decoration,
      child: Stack(
        fit: StackFit.expand,
        children: [
          _GradientOverlay(showBorder: isActive),
          Padding(
            padding: dimen.all.xxs,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GroupStatusLabel(status: status, label: statusLabel),
                    Ink(
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [
                            theme.semanticColorsTheme.greyShades.shade900
                                .withAlpha(51),
                            Colors.transparent,
                          ],
                        ),
                      ),
                      child: GestureDetector(
                        onTap: onToggleFavorite != null
                            ? () => onToggleFavorite!(!isFavorite)
                            : null,
                        child: Icon(
                          isFavorite
                              ? LMIcons.favoriteFilled
                              : LMIcons.favorite,
                          color: textColor,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: xxs),
                Text(
                  title,
                  style: theme.appTextTheme.body1.copyWith(
                    color: textColor,
                  ),
                ),
                const SizedBox(height: micro),
                SizedBox(
                  height: md + micro,
                  child: Text(
                    description,
                    style: theme.appTextTheme.body3.copyWith(
                      color: textColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SmallGroupCard extends StatelessWidget {
  const _SmallGroupCard({
    required this.decoration,
    required this.isActive,
    required this.title,
    required this.status,
  });

  final BoxDecoration decoration;
  final bool isActive;
  final String title;
  final GroupCardStatus status;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return Container(
      width: xxxl + xxsPlus,
      height: xxxl + xxsPlus,
      decoration: decoration,
      child: Stack(
        fit: StackFit.expand,
        children: [
          _GradientOverlay(
            showBorder: isActive,
          ),
          Padding(
            padding: dimen.all.xxs,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.appTextTheme.body2.copyWith(
                    color: theme.semanticColorsTheme.greyShades.shade100,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: micro,
            top: micro,
            child: GroupStatusLabel.small(
              status: status,
            ),
          ),
        ],
      ),
    );
  }
}

class _GradientOverlay extends StatelessWidget {
  const _GradientOverlay({this.showBorder = false});

  final bool showBorder;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;

    return DecoratedBox(
      decoration: BoxDecoration(
        border: showBorder
            ? Border.all(width: nano, color: theme.colorScheme.primary)
            : null,
        borderRadius: BorderRadius.circular(xxs),
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            theme.semanticColorsTheme.greyShades.shade900.withAlpha(179),
            Colors.transparent,
          ],
        ),
      ),
    );
  }
}
