import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';

class GroupStatusLabel extends StatelessWidget {
  const GroupStatusLabel({
    super.key,
    this.label,
    this.status = GroupCardStatus.unlocked,
  }) : _isSmall = false;

  const GroupStatusLabel.small({
    GroupCardStatus status = GroupCardStatus.unlocked,
  }) : label = '',
       _isSmall = true,
       status = status;

  final GroupCardStatus status;
  final String? label;
  final bool _isSmall;

  (Color?, Color?, Color?, IconData?) _getPillData(
    ThemeData theme,
    GroupCardStatus status,
  ) {
    return switch (status) {
      GroupCardStatus.soon => (
        theme.semanticColorsTheme.lightYellow,
        theme.semanticColorsTheme.yellow,
        theme.semanticColorsTheme.darkYellow,
        LMIcons.calendar,
      ),
      GroupCardStatus.premium => (
        theme.colorScheme.secondary,
        theme.colorScheme.primaryFixed,
        theme.colorScheme.primaryFixedDim,
        LMIcons.star,
      ),
      _ => (null, null, null, null),
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;

    final (lightColor, regularColor, darkColor, icon) = _getPillData(
      theme,
      status,
    );

    final container = _isSmall
        ? _SmallStatusLabel(
            lightColor: lightColor,
            regularColor: regularColor,
            darkColor: darkColor,
            icon: icon,
          )
        : _RegularStatusLabel(
            lightColor: lightColor,
            regularColor: regularColor,
            icon: icon,
            darkColor: darkColor,
            label: label,
          );

    return status == GroupCardStatus.unlocked ? emptyWidget : container;
  }
}

class _RegularStatusLabel extends StatelessWidget {
  const _RegularStatusLabel({
    required this.lightColor,
    required this.regularColor,
    required this.icon,
    required this.darkColor,
    required this.label,
  });

  final Color? lightColor;
  final Color? regularColor;
  final IconData? icon;
  final Color? darkColor;
  final String? label;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return Container(
      height: xs + micro,
      decoration: BoxDecoration(
        color: lightColor,
        borderRadius: BorderRadius.circular(md),
        border: Border.all(color: regularColor ?? theme.colorScheme.surface),
      ),
      child: Padding(
        padding: dimen.x.xxs + dimen.y.nano,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: xxs + nano,
              child: Icon(
                icon,
                size: xxsPlus,
                color: darkColor,
              ),
            ),
            const SizedBox(width: micro),
            Text(
              label ?? '',
              style: theme.appTextTheme.label3.copyWith(color: darkColor),
            ),
          ],
        ),
      ),
    );
  }
}

class _SmallStatusLabel extends StatelessWidget {
  const _SmallStatusLabel({
    required this.lightColor,
    required this.regularColor,
    required this.darkColor,
    required this.icon,
  });

  final Color? lightColor;
  final Color? regularColor;
  final Color? darkColor;
  final IconData? icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: xs + micro,
      height: xs + micro,
      decoration: BoxDecoration(
        color: lightColor,
        shape: BoxShape.circle,
        border: Border.all(
          color: regularColor ?? context.appTheme.colorScheme.surface,
        ),
      ),
      child: SizedBox.square(
        dimension: xxsPlus,
        child: Icon(
          icon,
          size: xxsPlus,
          color: darkColor,
        ),
      ),
    );
  }
}
