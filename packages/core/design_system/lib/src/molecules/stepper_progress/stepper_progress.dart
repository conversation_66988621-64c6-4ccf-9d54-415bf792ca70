import 'package:design_system/src/atoms/dimensions.dart';
import 'package:design_system/src/atoms/sizes.dart';
import 'package:design_system/src/theme/theme.dart';
import 'package:flutter/material.dart';

class StepperProgress extends StatelessWidget {
  const StepperProgress({
    required this.totalSteps,
    required this.currentStep,
    super.key,
    this.padding = xs,
  });

  final int totalSteps;
  final int currentStep;
  final double padding;

  List<Widget> _buildSteps() {
    return List.generate(
      totalSteps,
      (index) => _Step(
        isActive: currentStep == index,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: _buildSteps(),
    );
  }
}

class _Step extends StatelessWidget {
  const _Step({
    this.isActive = false,
  });

  final bool isActive;

  @override
  Widget build(BuildContext context) {
    final theme = context.appTheme;
    return Flexible(
      flex: isActive ? 2 : 1,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return AnimatedContainer(
            height: xxs,
            margin: dimen.x.xxs,
            curve: Curves.easeInOut,
            width: isActive ? constraints.maxWidth : xs,
            duration: isActive ? kThemeAnimationDuration : Duration.zero,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(xxs)),
              color: isActive
                  ? theme.colorScheme.primary
                  : theme.semanticColorsTheme.greyShades.shade200,
            ),
          );
        },
      ),
    );
  }
}
