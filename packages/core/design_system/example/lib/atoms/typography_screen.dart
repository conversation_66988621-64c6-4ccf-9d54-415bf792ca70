import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';

class TypographyScreen extends StatelessWidget {
  const TypographyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Typography'),
      ),
      body: const _Content(),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    final textTheme = context.appTheme.appTextTheme;
    return ListView(
      padding: dimen.all.xs,
      children: [
        Text('D1 Large 64', style: textTheme.display1),
        Text('D1 Large 56', style: textTheme.display2),
        Text('D1 Medium 44', style: textTheme.display3),
        Text('D1 Small 36', style: textTheme.display4),
        const Divider(),
        Text('H1 Large 32', style: textTheme.headline1),
        Text('H2 Medium 28', style: textTheme.headline2),
        Text('H3 Small 24', style: textTheme.headline3),
        Text('H4-Title Large 22', style: textTheme.headline4),
        Text('H5-Title Medium 16', style: textTheme.headline5),
        Text('H6-Title Small 14', style: textTheme.headline6),
        const Divider(),
        Text('B1 Large 16', style: textTheme.body1),
        Text('B2 Medium 14', style: textTheme.body2),
        Text('B3 Small 12', style: textTheme.body3),
        const Divider(),
        Text('L1 Large 14', style: textTheme.label1),
        Text('L2 Medium 12', style: textTheme.label2),
        Text('L3 Small 11', style: textTheme.label3),
      ],
    );
  }
}
