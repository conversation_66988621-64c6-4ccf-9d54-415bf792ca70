import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';

class ButtonsScreen extends StatelessWidget {
  const ButtonsScreen({super.key});

  List<Widget> _primaryButtonBlocked() => [
    PrimaryButton.blocked(
      title: 'Button',
      onPressed: () {},
    ),
    PrimaryButton.blocked(
      title: 'Button',
      leadingIcon: const Icon(Icons.add),
      onPressed: () {},
    ),
    PrimaryButton.blocked(
      title: 'Button',
      trailingIcon: const Icon(Icons.add),
      onPressed: () {},
    ),
    PrimaryButton.blocked(
      title: 'Button',
      leadingIcon: const Icon(Icons.add),
      trailingIcon: const Icon(Icons.add),
      onPressed: () {},
    ),
    PrimaryButton.blocked(
      title: 'Button',
    ),
  ];

  Iterable<Widget> _primayButtonResponsive() =>
      [
        PrimaryButton.responsive(
          title: 'Button',
          onPressed: () {},
        ),
        PrimaryButton.responsive(
          title: 'Button',
          leadingIcon: const Icon(Icons.add),
          onPressed: () {},
        ),
        PrimaryButton.responsive(
          title: 'Button',
          trailingIcon: const Icon(Icons.add),
          onPressed: () {},
        ),
        PrimaryButton.responsive(
          title: 'Button',
          leadingIcon: const Icon(Icons.add),
          trailingIcon: const Icon(Icons.add),
          onPressed: () {},
        ),
        PrimaryButton.responsive(
          title: 'Button',
        ),
        PrimaryButton.responsive(
          title: 'Button',
          onPressed: () {},
          isLoading: true,
        ),
      ].map(
        (e) => Padding(
          padding: dimen.x.md,
          child: e,
        ),
      );

  List<Widget> _secondaryButtonBlocked() => [
    SecondaryButton.blocked(
      title: 'Button',
      onPressed: () {},
    ),
    SecondaryButton.blocked(
      title: 'Button',
    ),
    SecondaryButton.blocked(
      leadingIcon: const Icon(Icons.add),
      trailingIcon: const Icon(Icons.add),
      title: 'Button',
      onPressed: () {},
    ),
    SecondaryButton.blocked(
      leadingIcon: const Icon(Icons.add),
      trailingIcon: const Icon(Icons.add),
      title: 'Button',
    ),
    SecondaryButton.blocked(
      title: 'Button',
      isLoading: true,
    ),
  ];

  Iterable<Widget> _secondaryButtonResponsive() =>
      [
        SecondaryButton.responsive(
          title: 'Button',
          onPressed: () {},
        ),
        SecondaryButton.responsive(
          title: 'Button',
        ),
        SecondaryButton.responsive(
          leadingIcon: const Icon(Icons.add),
          trailingIcon: const Icon(Icons.add),
          title: 'Button',
          onPressed: () {},
        ),
        SecondaryButton.responsive(
          leadingIcon: const Icon(Icons.add),
          trailingIcon: const Icon(Icons.add),
          title: 'Button',
        ),
        SecondaryButton.responsive(
          title: 'Button',
          isLoading: true,
        ),
      ].map(
        (e) => Padding(
          padding: dimen.x.md,
          child: e,
        ),
      );
  Iterable<Widget> _tartiaryButtonResponsive() =>
      [
        TertiaryButton.responsive(
          title: 'Button',
          onPressed: () {},
        ),
        TertiaryButton.responsive(
          title: 'Button',
        ),
        TertiaryButton.responsive(
          leadingIcon: const Icon(Icons.add),
          trailingIcon: const Icon(Icons.add),
          title: 'Button',
          onPressed: () {},
        ),
        TertiaryButton.responsive(
          leadingIcon: const Icon(Icons.add),
          trailingIcon: const Icon(Icons.add),
          title: 'Button',
        ),
        TertiaryButton.responsive(
          title: 'Button',
          isLoading: true,
        ),
      ].map(
        (e) => Padding(
          padding: dimen.x.md,
          child: e,
        ),
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Buttons'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            const Text('Primary Button (Blocked)'),
            const SizedBox(height: xs),
            ..._primaryButtonBlocked(),
            const SizedBox(height: lg),
            const Text('Primary Button (Responsive)'),
            const SizedBox(height: xs),
            ..._primayButtonResponsive(),
            const SizedBox(height: lg),
            const Text('Secondary Button (Blocked)'),
            const SizedBox(height: xs),
            ..._secondaryButtonBlocked(),
            const SizedBox(height: lg),
            const Text('Secondary Button (Responsive)'),
            const SizedBox(height: xs),
            ..._secondaryButtonResponsive(),
            const SizedBox(height: lg),
            const Text('Tertiary Button (Responsive)'),
            const SizedBox(height: xs),
            ..._tartiaryButtonResponsive(),
            const SizedBox(height: xl),
          ],
        ),
      ),
    );
  }
}
