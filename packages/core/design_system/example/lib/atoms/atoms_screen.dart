import 'package:design_system/design_system.dart';
import 'package:example/atoms/buttons_screen.dart';
import 'package:example/atoms/typography_screen.dart';
import 'package:example/utils/navigation_utils.dart';
import 'package:flutter/material.dart';

class AtomsScreen extends StatelessWidget {
  const AtomsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Atoms'),
      ),
      body: ListView(
        children: [
          ListTile(
            title: const Text('Buttons'),
            onTap: () => goTo(context, const ButtonsScreen()),
            trailing: const Icon(LMIcons.chevronRight),
          ),
          ListTile(
            title: const Text('Typography'),
            onTap: () => goTo(context, const TypographyScreen()),
            trailing: const Icon(LMIcons.chevronRight),
          ),
        ],
      ),
    );
  }
}
