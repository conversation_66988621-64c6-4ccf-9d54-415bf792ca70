import 'package:design_system/design_system.dart';
import 'package:example/molecules/avatar_screen.dart';
import 'package:example/molecules/group_card_screen.dart';
import 'package:example/utils/navigation_utils.dart';
import 'package:flutter/material.dart';

class MoleculesScreen extends StatelessWidget {
  const MoleculesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Molecules'),
      ),
      body: ListView(
        children: [
          ListTile(
            title: const Text('Avatars'),
            onTap: () => goTo(context, const AvatarScreen()),
            trailing: const Icon(LMIcons.chevronRight),
          ),
          ListTile(
            title: const Text('Group Cards'),
            onTap: () => goTo(context, const GroupCardScreen()),
            trailing: const Icon(LMIcons.chevronRight),
          ),
        ],
      ),
    );
  }
}
