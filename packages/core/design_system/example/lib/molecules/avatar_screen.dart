import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';

class AvatarScreen extends StatelessWidget {
  const AvatarScreen({super.key});

  final _url = 'https://picsum.photos/id/64/900';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Avatars')),
      body: ListView(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        padding: dimen.all.xs,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  StoryAvatar.add(
                    image: NetworkImage(_url),
                    label: 'Tu Historia',
                    onTap: () {},
                  ),
                  const SizedBox(width: xxsPlus),
                  StoryAvatar(
                    image: NetworkImage(_url),
                    label: 'Centro',
                    hasNewStories: true,
                    onTap: () {},
                  ),
                ],
              ),
              const SizedBox(height: xs),
              Row(
                children: [
                  StoryAvatar(
                    status: StoryAvatarStatus.onlineVerified,
                    hasNewStories: true,
                    image: NetworkImage(_url),
                    label: '<PERSON>',
                    onTap: () {},
                  ),
                  const SizedBox(width: xxsPlus),
                  StoryAvatar(
                    status: StoryAvatarStatus.online,
                    hasNewStories: true,
                    image: NetworkImage(_url),
                    label: 'Jimmy El Metralletas',
                    onTap: () {},
                  ),
                  const SizedBox(width: xxsPlus),
                  StoryAvatar(
                    status: StoryAvatarStatus.offlineVerified,
                    hasNewStories: true,
                    image: NetworkImage(_url),
                    label: 'José García',
                    onTap: () {},
                  ),
                ],
              ),
              const SizedBox(height: xs),
              Row(
                children: [
                  StoryAvatar(
                    image: NetworkImage(_url),
                    onTap: () {},
                  ),
                  const SizedBox(width: xxsPlus),
                  StoryAvatar(
                    status: StoryAvatarStatus.online,
                    image: NetworkImage(_url),
                    onTap: () {},
                  ),
                  const SizedBox(width: xxsPlus),
                  StoryAvatar(
                    status: StoryAvatarStatus.offlineVerified,
                    image: NetworkImage(_url),
                    onTap: () {},
                  ),
                ],
              ),
              const SizedBox(height: xs),
              Row(
                children: [
                  StoryAvatar.loading(),
                  const SizedBox(width: xxsPlus),
                  StoryAvatar(
                    status: StoryAvatarStatus.online,
                    image: NetworkImage(_url),
                    onTap: () {},
                  ),
                  const SizedBox(width: xxsPlus),
                  StoryAvatar(
                    status: StoryAvatarStatus.offlineVerified,
                    image: NetworkImage(_url),
                    onTap: () {},
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
