import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';

class GroupCardScreen extends StatelessWidget {
  const GroupCardScreen({super.key});

  final _url = 'https://picsum.photos/id/64/152/200';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Group Cards')),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: xxxl + xxsPlus,
            child: ListView(
              shrinkWrap: true,
              padding: dimen.x.xs,
              scrollDirection: Axis.horizontal,
              children: [
                GroupCard.small(
                  title: 'Tríos',
                  image: NetworkImage(_url),
                  onTap: () {},
                ),
                const SizedBox(width: xxs),
                GroupCard.small(
                  title: 'Tríos',
                  image: NetworkImage(_url),
                  isActive: true,
                  onTap: () {},
                ),
                const SizedBox(width: xxs),
                GroupCard.small(
                  title: 'Tríos',
                  image: NetworkImage(_url),
                  status: GroupCardStatus.premium,
                  isActive: true,
                  onTap: () {},
                ),
                const SizedBox(width: xxs),
                GroupCard.small(
                  title: 'Tríos',
                  image: NetworkImage(_url),
                  status: GroupCardStatus.soon,
                  onTap: () {},
                ),
                const SizedBox(width: xxs),
              ],
            ),
          ),
          const SizedBox(
            height: xs,
          ),
          SizedBox(
            height: 200,
            child: ListView(
              shrinkWrap: true,
              padding: dimen.x.xs,
              scrollDirection: Axis.horizontal,
              children: [
                GroupCard(
                  title: 'Tríos',
                  description: 'Parejas que buscan parejas',
                  statusLabel: 'Próximamente',
                  image: NetworkImage(_url),
                  status: GroupCardStatus.soon,
                  onTap: () {},
                ),
                const SizedBox(width: xxs),
                GroupCard(
                  title: 'Tríos',
                  description: 'Parejas que buscan parejas',
                  image: NetworkImage(_url),
                  isActive: true,
                  isFavorite: true,
                ),
                const SizedBox(width: xxs),
                GroupCard(
                  title: 'Tríos',
                  description: 'Parejas que buscan parejas',
                  image: NetworkImage(_url),
                  statusLabel: 'Premium',
                  status: GroupCardStatus.premium,
                  isFavorite: true,
                ),
                const SizedBox(width: xxs),
                GroupCard(
                  title: 'Tríos',
                  description: 'Parejas que buscan parejas',
                  image: NetworkImage(_url),
                  isActive: true,
                  isFavorite: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
