// ignore_for_file: invalid_use_of_internal_member

import 'package:design_system/design_system.dart';
import 'package:example/atoms/atoms_screen.dart';
import 'package:example/molecules/molecules_screen.dart';
import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  int currentIndex = 0;

  Widget? _getScreenByIndex() => switch (currentIndex) {
    0 => const AtomsScreen(),
    1 => const MoleculesScreen(),
    _ => const Placeholder(),
  };

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Lunch Manager Kitchen Sink',
      theme: AppTheme.light,
      darkTheme: AppTheme.dark,
      home: Scaffold(
        body: _getScreenByIndex(),
        bottomNavigationBar: AppBottomNavbar(
          currentIndex: currentIndex,
          onTap: (index) => setState(() {
            currentIndex = index;
          }),
          items: [
            AppBottomNavbarItem(
              icon: Icon(LMIcons.fa.atom),
              label: 'Atoms',
            ),
            AppBottomNavbarItem(
              icon: Icon(LMIcons.fa.molecule),
              label: 'Molecules',
            ),
            AppBottomNavbarItem(
              icon: Icon(LMIcons.fa.organism),
              label: 'Organisms',
            ),
          ],
        ),
      ),
    );
  }
}
