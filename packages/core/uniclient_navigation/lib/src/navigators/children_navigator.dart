import 'dart:async';

import 'package:children/children.dart';
import 'package:injectable/injectable.dart';
import 'package:uniclient_navigation/src/router/router.dart';

@Injectable(as: ChildrenNavigator)
class ClientChildrenNavigator implements ChildrenNavigator {
  const ClientChildrenNavigator(this._router);

  final ClientRouter _router;

  @override
  FutureOr<void> pushHome() => _router.replace(const AuthFlowRoute());

  @override
  FutureOr<void> pushRegisterChild() =>
      _router.push(const RegisterChildFlowRoute());

  @override
  FutureOr<void> goBackToChildrenList() => _router.maybePop();

  @override
  FutureOr<void> pushChildDetails(Child child) =>
      _router.push(ChildDetailsFlowRoute(child: child));

  @override
  FutureOr<void> pushAllChildrenReport(Child child) =>
      _router.push(const AllChildrenReportFlowRoute());

  @override
  FutureOr<void> pushRegisteredChildrenList() =>
      _router.push(const RegisteredChildrenListFlowRoute());
}
