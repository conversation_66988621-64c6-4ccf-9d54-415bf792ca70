import 'dart:async';

import 'package:auth/auth.dart';
import 'package:injectable/injectable.dart';
import 'package:uniclient_navigation/src/router/router.dart';

@Injectable(as: AuthNavigator)
class ClientAuthNavigator implements AuthNavigator {
  const ClientAuthNavigator(this._router);

  final ClientRouter _router;

  @override
  FutureOr<void> pushEmailVerification() =>
      _router.replace(const EmailVerificationFlowRoute());

  @override
  FutureOr<void> pushRegister() => _router.replace(const AuthFlowRoute());

  @override
  FutureOr<void> pushRegisterChildren() =>
      _router.replace(const ChildrenListFlowRoute());

  @override
  FutureOr<void> pushRegisterUserProfile() =>
      _router.replace(const RegisterUserProfileFlowRoute());

  @override
  FutureOr<void> pushAllChildrenReport() =>
      _router.replace(const AllChildrenReportFlowRoute());

  @override
  FutureOr<void> pushRegisteredChildrenList() =>
      _router.replace(const RegisteredChildrenListFlowRoute());
}
