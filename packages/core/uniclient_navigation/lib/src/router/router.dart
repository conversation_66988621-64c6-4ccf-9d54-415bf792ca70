import 'package:auto_route/auto_route.dart';
import 'package:children/children.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:uniclient_navigation/src/route/routes.dart';

part 'router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'AutoRouteWrapper,Route')
@lazySingleton
class ClientRouter extends RootStackRouter {
  ClientRouter(
    GlobalKey<NavigatorState> navigatorKey,
  ) : _routes = _createRoutes(
        clientRoutes,
        [],
      ),
      super(navigatorKey: navigatorKey);

  final List<AutoRoute> _routes;

  static List<AutoRoute> _createRoutes(
    List<AutoRoute> routes,
    List<AutoRouteGuard> guards,
  ) => routes
      .map(
        (route) => route.copyWith(
          guards: [
            ...route.guards,
            ...guards,
          ],
        ),
      )
      .toList();

  @override
  List<AutoRoute> get routes => _routes;

  @override
  RouteType get defaultRouteType => const RouteType.adaptive();

  RouterConfig<UrlState> clientConfig({
    WidgetBuilder? placeholder,
    NavigatorObserversBuilder navigatorObservers =
        AutoRouterDelegate.defaultNavigatorObserversBuilder,
  }) => super.config(
    placeholder: placeholder,
    navigatorObservers: () => [
      ...navigatorObservers(),
    ],
  );
}
