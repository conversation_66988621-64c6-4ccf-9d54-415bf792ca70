part of 'routes.dart';

@RoutePage()
class InitialVerificationFlowAutoRouteWrapper extends InitialVerificationPage {
  const InitialVerificationFlowAutoRouteWrapper({super.key});
}

@RoutePage()
class AuthFlowAutoRouteWrapper extends AuthPage {
  const AuthFlowAutoRouteWrapper({super.key});
}

@RoutePage()
class EmailVerificationFlowAutoRouteWrapper extends EmailVerificationPage {
  const EmailVerificationFlowAutoRouteWrapper({super.key});
}

@RoutePage()
class RegisterUserProfileFlowAutoRouteWrapper extends RegisterUserProfilePage {
  const RegisterUserProfileFlowAutoRouteWrapper({super.key});
}

@RoutePage()
class RegisterChildFlowAutoRouteWrapper extends RegisterChildPage {
  const RegisterChildFlowAutoRouteWrapper({super.key});
}

@RoutePage()
class ChildrenListFlowAutoRouteWrapper extends ChildrenListPage {
  const ChildrenListFlowAutoRouteWrapper({super.key});
}

@RoutePage()
class ChildDetailsFlowAutoRouteWrapper extends ChildDetails {
  const ChildDetailsFlowAutoRouteWrapper({required super.child, super.key});
}

@RoutePage()
class HomeFlowAutoRouteWrapper extends Placeholder {
  const HomeFlowAutoRouteWrapper({super.key});
}

@RoutePage()
class AllChildrenReportFlowAutoRouteWrapper extends AllChildrenReport {
  const AllChildrenReportFlowAutoRouteWrapper({super.key});
}

@RoutePage()
class RegisteredChildrenListFlowAutoRouteWrapper
    extends RegisteredChildrenListPage {
  const RegisteredChildrenListFlowAutoRouteWrapper({super.key});
}
