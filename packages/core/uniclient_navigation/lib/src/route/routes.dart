import 'package:auth/auth.dart';
import 'package:auto_route/auto_route.dart';
import 'package:children/children.dart';
import 'package:flutter/material.dart';
import 'package:uniclient_navigation/src/router/router.dart';

part 'wrappers.dart';

final clientRoutes = [
  _initialRoute,
  _authRoute,
  _emailVerificationRoute,
  _registerUserProfile,
  _childrenList,
  _registerChild,
  _homeRoute,
  _childDetails,
  _allChildrenListReport,
  _registeredChildrenList,
];

final _initialRoute = AutoRoute(
  path: '/',
  page: InitialVerificationFlowRoute.page,
  initial: true,
);

final _authRoute = AutoRoute(
  path: '/auth',
  page: AuthFlowRoute.page,
);

final _emailVerificationRoute = AutoRoute(
  path: '/verify_email',
  page: EmailVerificationFlowRoute.page,
);

final _registerUserProfile = AutoRoute(
  path: '/register_user_profile',
  page: RegisterUserProfileFlowRoute.page,
);

final _registerChild = AutoRoute(
  path: '/register_child',
  page: RegisterChildFlowRoute.page,
);

final _childrenList = AutoRoute(
  path: '/children',
  page: ChildrenListFlowRoute.page,
  // children: [_registerChild],
);

final _childDetails = AutoRoute(
  path: '/details',
  page: ChildDetailsFlowRoute.page,
);

final _homeRoute = AutoRoute(
  path: '/children',
  page: HomeFlowRoute.page,
);

final _allChildrenListReport = AutoRoute(
  path: '/all_children',
  page: AllChildrenReportFlowRoute.page,
);

final _registeredChildrenList = AutoRoute(
  path: '/registered_children',
  page: RegisteredChildrenListFlowRoute.page,
);
