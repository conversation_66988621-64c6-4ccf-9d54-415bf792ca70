// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auth/auth.dart' as _i662;
import 'package:children/children.dart' as _i864;
import 'package:flutter/material.dart' as _i409;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:uniclient_navigation/src/navigators/auth_navigator.dart'
    as _i482;
import 'package:uniclient_navigation/src/navigators/children_navigator.dart'
    as _i133;
import 'package:uniclient_navigation/src/router/router.dart' as _i951;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    gh.lazySingleton<_i951.ClientRouter>(
      () => _i951.ClientRouter(gh<_i409.GlobalKey<_i409.NavigatorState>>()),
    );
    gh.factory<_i662.AuthNavigator>(
      () => _i482.ClientAuthNavigator(gh<_i951.ClientRouter>()),
    );
    gh.factory<_i864.ChildrenNavigator>(
      () => _i133.ClientChildrenNavigator(gh<_i951.ClientRouter>()),
    );
    return this;
  }
}
