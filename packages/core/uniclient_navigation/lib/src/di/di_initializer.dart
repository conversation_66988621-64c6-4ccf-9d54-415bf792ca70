import 'dart:async';

import 'package:app_di/app_di.dart';
import 'package:get_it/get_it.dart';
import 'package:uniclient_navigation/src/di/di_initializer.config.dart';

class ClientNavigationDIInitializer extends ClientDIInitializer {
  const ClientNavigationDIInitializer() : super(_init);
}

@diInitializerInit
FutureOr<GetIt> _init(GetIt getIt, String? environment) =>
    getIt.init(environment: environment);
