{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Parents - WIP",
            "request": "launch",
            "type": "dart",
            "program": "apps/uniclient/lib/entrypoints/parents/main_wip.dart",
            "args": [
                "--flavor",
                "parentsWip"
            ],
            "toolArgs": [
                "--dart-define-from-file",
                "${workspaceFolder}/.secrets/secrets_client_parentsWip.json"
            ]
        },
        {
            "name": "Manager - WIP",
            "request": "launch",
            "type": "dart",
            "program": "apps/uniclient/lib/entrypoints/manager/main_wip.dart",
            "args": [
                "--flavor",
                "managerWip"
            ],
            "toolArgs": [
                "--dart-define-from-file",
                "${workspaceFolder}/.secrets/secrets_client_managerWip.json"
            ]
        },
        {
            "name": "Parents - WIP [Web]",
            "request": "launch",
            "type": "dart",
            "program": "apps/uniclient/lib/entrypoints/parents/main_wip.dart",
            "toolArgs": [
                "--release",
                "--dart-define-from-file",
                "${workspaceFolder}/.secrets/secrets_client_parentsWip.json"
            ]
        },
        {
            "name": "Kitchen Sink",
            "cwd": "packages/core/design_system/example",
            "request": "launch",
            "type": "dart"
        },
    ]
}