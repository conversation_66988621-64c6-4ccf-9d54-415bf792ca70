# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on PR
on: pull_request
permissions:
  checks: write
  contents: read
  pull-requests: write

jobs:
  build_and_preview:
    if: ${{ github.event.pull_request.head.repo.full_name == github.repository }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install Flutter with cache
        uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version: 3.24.0
          cache: true
          cache-key: flutter-:os:-:channel:-:version:-:arch:-

      - name: Download Flutter packages
        run: |
          sh ./scripts/setup.sh
          cd apps/uniclient
          melos run pub-get-p
          flutter pub get


      - name: Build Flutter web
        run: | 
          cd apps/uniclient
          flutter build web --release -t "lib/entrypoints/main_wip.dart"

      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_LUNCH_MANAGER_WIP }}
          projectId: lunch-manager-wip
          entryPoint: apps/uniclient
        env:
          FIREBASE_CLI_EXPERIMENTS: webframeworks
