name: "PR Title Checker"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize
      - reopened

jobs:
  check:
    permissions: write-all
    runs-on: ubuntu-latest
    steps:
      - name: Check PR Title
        env: 
          TITLE: ${{ github.event.pull_request.title }}
          REGEXP: ^(ZYR-[0-9]+|No-Jira):.*
        run: |
          [[ "${TITLE}" =~ ${REGEXP} ]] && echo "PR Title is OK"